/* news.html 专用样式 */

/* 新闻页面主容器 */
.news-main {
  padding-top: 80px;
}

/* 新闻页面头部 */
.news-hero {
  padding: 80px 0 60px;
  background: #ffffff;
  position: relative;
  overflow: hidden;
}

.news-hero .container {
  max-width: 95%;
  margin: 0 auto;
  padding: 0 max(3%, 30px);
}

.news-header {
  position: relative;
  z-index: 2;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 80px;
  min-height: 400px;
}

.header-left {
  flex: 0 0 auto;
  min-width: 350px;
}

.news-title {
  font-size: 8rem;
  font-weight: 900;
  color: #f0f0f0;
  text-transform: uppercase;
  letter-spacing: -0.02em;
  line-height: 0.9;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, #dcfce7 0%, #22c55e 50%, #16a34a 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.header-right {
  flex: 1;
  margin-left: 100px;
}

.news-info {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(226, 232, 240, 0.3);
  border-radius: 24px;
  padding: 40px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
}

.news-info-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 20px;
  line-height: 1.2;
}

.news-description {
  font-size: 1.2rem;
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0;
}

.highlight-text {
  color: #16a34a;
  font-weight: 600;
}

/* 浮动的毛玻璃球 - 绿色主题 */
.glass-ball-green-1 {
  position: absolute;
  top: 15%;
  left: 8%;
  width: 250px;
  height: 250px;
  background: radial-gradient(circle, rgba(34, 197, 94, 0.12) 0%, rgba(22, 163, 74, 0.08) 50%, transparent 70%);
  backdrop-filter: blur(20px);
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.3);
  animation: floatGreenGlassBall1 15s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
}

.glass-ball-green-2 {
  position: absolute;
  top: 45%;
  right: 12%;
  width: 180px;
  height: 180px;
  background: radial-gradient(circle, rgba(22, 163, 74, 0.1) 0%, rgba(21, 128, 61, 0.06) 50%, transparent 70%);
  backdrop-filter: blur(15px);
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.25);
  animation: floatGreenGlassBall2 18s ease-in-out infinite reverse;
  pointer-events: none;
  z-index: 1;
}

.glass-ball-green-3 {
  position: absolute;
  bottom: 20%;
  left: 15%;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(220, 252, 231, 0.08) 0%, rgba(34, 197, 94, 0.05) 50%, transparent 70%);
  backdrop-filter: blur(18px);
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: floatGreenGlassBall3 20s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
}

/* 绿色毛玻璃球动画 */
@keyframes floatGreenGlassBall1 {
  0%, 100% { transform: translate(0, 0) rotate(0deg); }
  25% { transform: translate(30px, -20px) rotate(90deg); }
  50% { transform: translate(-20px, 30px) rotate(180deg); }
  75% { transform: translate(40px, 10px) rotate(270deg); }
}

@keyframes floatGreenGlassBall2 {
  0%, 100% { transform: translate(0, 0) scale(1); }
  33% { transform: translate(-25px, 20px) scale(1.1); }
  66% { transform: translate(35px, -15px) scale(0.9); }
}

@keyframes floatGreenGlassBall3 {
  0%, 100% { transform: translate(0, 0) rotate(0deg) scale(1); }
  50% { transform: translate(-30px, -25px) rotate(180deg) scale(1.05); }
}

/* 新闻列表区域 */
.news-list {
  padding: 80px 0;
  background: #f8fafb;
}

.news-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: 40px;
  margin-top: 40px;
}

.news-card {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid rgba(226, 232, 240, 0.3);
  position: relative;
}

.news-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.12);
}

.news-image {
  position: relative;
  height: 250px;
  overflow: hidden;
  background: linear-gradient(135deg, #dcfce7 0%, #22c55e 100%);
}

.news-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.news-card:hover .news-img {
  transform: scale(1.05);
}

.news-date-badge {
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(22, 163, 74, 0.9);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  backdrop-filter: blur(10px);
}

.news-content {
  padding: 30px;
}

.news-category {
  display: inline-block;
  background: rgba(22, 163, 74, 0.1);
  color: #16a34a;
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  margin-bottom: 16px;
}

.news-card-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 16px;
  line-height: 1.3;
}

.news-summary {
  font-size: 1rem;
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 20px;
}

.news-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 20px;
  border-top: 1px solid rgba(226, 232, 240, 0.5);
}

.news-author {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.news-read-more {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  color: #16a34a;
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.news-read-more:hover {
  color: #15803d;
  transform: translateX(3px);
}

.news-read-more i {
  transition: transform 0.3s ease;
}

.news-read-more:hover i {
  transform: translateX(3px);
}

/* 新闻筛选器 */
.news-filters {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 40px;
  flex-wrap: wrap;
}

.filter-btn {
  padding: 10px 20px;
  border: 2px solid rgba(22, 163, 74, 0.2);
  background: white;
  color: var(--text-secondary);
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  font-weight: 500;
}

.filter-btn:hover,
.filter-btn.active {
  background: #16a34a;
  color: white;
  border-color: #16a34a;
}

/* 分页 */
.news-pagination {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 60px;
}

.pagination-btn {
  width: 40px;
  height: 40px;
  border: 1px solid rgba(22, 163, 74, 0.3);
  background: white;
  color: var(--text-secondary);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
}

.pagination-btn:hover,
.pagination-btn.active {
  background: #16a34a;
  color: white;
  border-color: #16a34a;
}

/* 响应式设计 - 移动端适配 */
@media (max-width: 768px) {
  .news-hero {
    padding: 60px 0 40px;
  }

  .header-content {
    flex-direction: column;
    gap: 40px;
    min-height: auto;
    text-align: center;
  }

  .header-left {
    min-width: auto;
    width: 100%;
  }

  .news-title {
    font-size: 4rem;
  }

  .header-right {
    margin-left: 0;
    width: 100%;
  }

  .news-info {
    padding: 30px 20px;
  }

  .news-info-title {
    font-size: 2rem;
  }

  .news-description {
    font-size: 1rem;
  }

  .news-grid {
    grid-template-columns: 1fr;
    gap: 30px;
    margin-top: 30px;
  }

  .news-card {
    border-radius: 16px;
  }

  .news-image {
    height: 200px;
  }

  .news-content {
    padding: 20px;
  }

  .news-card-title {
    font-size: 1.3rem;
  }

  .news-filters {
    gap: 10px;
    margin-bottom: 30px;
  }

  .filter-btn {
    padding: 8px 16px;
    font-size: 0.85rem;
  }

  .news-pagination {
    margin-top: 40px;
    gap: 8px;
  }

  .pagination-btn {
    width: 35px;
    height: 35px;
    font-size: 0.9rem;
  }

  /* 隐藏部分毛玻璃球以减少移动端视觉干扰 */
  .glass-ball-green-3 {
    display: none;
  }

  .glass-ball-green-1,
  .glass-ball-green-2 {
    transform: scale(0.6);
  }
}

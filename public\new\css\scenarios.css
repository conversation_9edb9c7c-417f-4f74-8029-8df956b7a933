/* scenarios.html 专用样式 */

/* 应用场景页面主容器 */
.scenarios-main {
  padding-top: 80px;
}

/* 应用场景头部 */
.scenarios-hero {
  padding: 80px 0 60px;
  background: #ffffff;
  position: relative;
  overflow: hidden;
}

.scenarios-hero .container {
  max-width: 95%;
  margin: 0 auto;
  padding: 0 max(3%, 30px);
}

.scenarios-header {
  position: relative;
  z-index: 2;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 80px;
  min-height: 400px;
}

.header-left {
  flex: 0 0 auto;
  min-width: 350px;
}

.scenarios-title {
  font-size: 8rem;
  font-weight: 900;
  color: #f0f0f0;
  text-transform: uppercase;
  letter-spacing: -0.02em;
  line-height: 0.9;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, #fef3c7 0%, #fbbf24 50%, #f59e0b 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.header-right {
  flex: 1;
  margin-left: 100px;
}

.scenarios-info {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(226, 232, 240, 0.3);
  border-radius: 24px;
  padding: 40px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
}

.scenarios-info-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 20px;
  line-height: 1.2;
}

.scenarios-description {
  font-size: 1.2rem;
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0;
}

.highlight-text {
  color: #f59e0b;
  font-weight: 600;
}

/* 浮动的毛玻璃球 - 橙色主题 */
.glass-ball-orange-1 {
  position: absolute;
  top: 15%;
  left: 8%;
  width: 250px;
  height: 250px;
  background: radial-gradient(circle, rgba(251, 191, 36, 0.12) 0%, rgba(245, 158, 11, 0.08) 50%, transparent 70%);
  backdrop-filter: blur(20px);
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.3);
  animation: floatOrangeGlassBall1 15s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
}

.glass-ball-orange-2 {
  position: absolute;
  top: 45%;
  right: 12%;
  width: 180px;
  height: 180px;
  background: radial-gradient(circle, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.06) 50%, transparent 70%);
  backdrop-filter: blur(15px);
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.25);
  animation: floatOrangeGlassBall2 18s ease-in-out infinite reverse;
  pointer-events: none;
  z-index: 1;
}

.glass-ball-orange-3 {
  position: absolute;
  bottom: 20%;
  left: 15%;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(254, 243, 199, 0.08) 0%, rgba(251, 191, 36, 0.05) 50%, transparent 70%);
  backdrop-filter: blur(18px);
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: floatOrangeGlassBall3 20s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
}

/* 橙色毛玻璃球动画 */
@keyframes floatOrangeGlassBall1 {
  0%, 100% { transform: translate(0, 0) rotate(0deg); }
  25% { transform: translate(30px, -20px) rotate(90deg); }
  50% { transform: translate(-20px, 30px) rotate(180deg); }
  75% { transform: translate(40px, 10px) rotate(270deg); }
}

@keyframes floatOrangeGlassBall2 {
  0%, 100% { transform: translate(0, 0) scale(1); }
  33% { transform: translate(-25px, 20px) scale(1.1); }
  66% { transform: translate(35px, -15px) scale(0.9); }
}

@keyframes floatOrangeGlassBall3 {
  0%, 100% { transform: translate(0, 0) rotate(0deg) scale(1); }
  50% { transform: translate(-30px, -25px) rotate(180deg) scale(1.05); }
}

/* 场景展示区域 */
.scenarios-showcase {
  padding: 80px 0;
  background: #f8fafb;
}

.scenarios-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 40px;
  margin-top: 40px;
}

.scenario-card {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid rgba(226, 232, 240, 0.3);
  position: relative;
}

.scenario-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.12);
}

.scenario-image {
  position: relative;
  height: 280px;
  overflow: hidden;
  background: linear-gradient(135deg, #fef3c7 0%, #fbbf24 100%);
}

.scenario-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.scenario-card:hover .scenario-img {
  transform: scale(1.05);
}

.scenario-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, rgba(245, 158, 11, 0.1) 0%, rgba(251, 191, 36, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.scenario-card:hover .scenario-overlay {
  opacity: 1;
}

.scenario-content {
  padding: 30px;
}

.scenario-category {
  display: inline-block;
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  margin-bottom: 16px;
}

.scenario-title {
  font-size: 1.6rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 16px;
  line-height: 1.3;
}

.scenario-summary {
  font-size: 1rem;
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 20px;
}

.scenario-features {
  list-style: none;
  padding: 0;
  margin: 0;
}

.scenario-features li {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 8px;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.scenario-features li::before {
  content: '✓';
  color: #f59e0b;
  font-weight: bold;
  font-size: 1rem;
}

.scenario-action {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid rgba(226, 232, 240, 0.5);
}

.scenario-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
  color: white;
  text-decoration: none;
  border-radius: 12px;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.scenario-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(245, 158, 11, 0.3);
}

.scenario-btn i {
  transition: transform 0.3s ease;
}

.scenario-btn:hover i {
  transform: translateX(3px);
}

/* 统计数据区域 */
.scenarios-stats {
  padding: 80px 0;
  background: linear-gradient(135deg, #fef3c7 0%, #fbbf24 100%);
  color: white;
  text-align: center;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 40px;
  margin-top: 40px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 3rem;
  font-weight: 900;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
}

.stat-label {
  font-size: 1.1rem;
  opacity: 0.9;
  font-weight: 500;
}

/* 响应式设计 - 移动端适配 */
@media (max-width: 768px) {
  .scenarios-hero {
    padding: 60px 0 40px;
  }

  .header-content {
    flex-direction: column;
    gap: 40px;
    min-height: auto;
    text-align: center;
  }

  .header-left {
    min-width: auto;
    width: 100%;
  }

  .scenarios-title {
    font-size: 4rem;
  }

  .header-right {
    margin-left: 0;
    width: 100%;
  }

  .scenarios-info {
    padding: 30px 20px;
  }

  .scenarios-info-title {
    font-size: 2rem;
  }

  .scenarios-description {
    font-size: 1rem;
  }

  .scenarios-grid {
    grid-template-columns: 1fr;
    gap: 30px;
    margin-top: 30px;
  }

  .scenario-card {
    border-radius: 16px;
  }

  .scenario-image {
    height: 220px;
  }

  .scenario-content {
    padding: 20px;
  }

  .scenario-title {
    font-size: 1.4rem;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
  }

  .stat-number {
    font-size: 2.5rem;
  }

  .stat-label {
    font-size: 1rem;
  }

  /* 隐藏部分毛玻璃球以减少移动端视觉干扰 */
  .glass-ball-orange-3 {
    display: none;
  }

  .glass-ball-orange-1,
  .glass-ball-orange-2 {
    transform: scale(0.6);
  }
}

/* 公共样式文件 - 所有页面共用的基础样式 */

/* 页面整体滑动动画初始状态 */
.page-section {
  opacity: 0;
  transform: translateY(60px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.page-section.animate-in {
  opacity: 1;
  transform: translateY(0);
}

/* 从左滑入效果 */
.slide-in-left {
  opacity: 0;
  transform: translateX(-80px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.slide-in-left.animate-in {
  opacity: 1;
  transform: translateX(0);
}

/* 从右滑入效果 */
.slide-in-right {
  opacity: 0;
  transform: translateX(80px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.slide-in-right.animate-in {
  opacity: 1;
  transform: translateX(0);
}

/* 缩放滑入效果 */
.slide-in-scale {
  opacity: 0;
  transform: scale(0.9) translateY(40px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.slide-in-scale.animate-in {
  opacity: 1;
  transform: scale(1) translateY(0);
}

/* 全局重置和基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 确保HTML和BODY完全消除空白 */
html,
body {
  margin: 0 !important;
  padding: 0 !important;
  height: 100%;
  width: 100%;
  border: 0;
  outline: 0;
}

/* 强制重置可能影响布局的元素 */
html {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

body {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

/* 确保页面第一个元素不会影响导航栏 */
body>*:first-child {
  margin-top: 0 !important;
  padding-top: 0;
}

/* 消除任何可能的默认浏览器样式 */
nav,
header {
  margin: 0 !important;
  padding: 0;
}

/* 兼容性重置 - 消除webkit和其他浏览器的默认样式 */
* {
  -webkit-margin-before: 0;
  -webkit-margin-after: 0;
  -webkit-margin-start: 0;
  -webkit-margin-end: 0;
  -webkit-padding-before: 0;
  -webkit-padding-after: 0;
  -webkit-padding-start: 0;
  -webkit-padding-end: 0;
}

/* CSS变量定义 */
:root {
  /* 现代科技色彩主题 */
  --primary-blue: #0066ff;
  --secondary-blue: #003d99;
  --accent-cyan: #00d4ff;
  --dark-blue: #001f3f;
  --text-primary: #1a1a1a;
  --text-secondary: #666666;
  --text-light: #999999;
  --bg-white: #ffffff;
  --bg-light: #f8fafb;
  --bg-dark: #0a0f1a;
  --border-light: #e5e7eb;
  --border-dark: #374151;

  /* 渐变 */
  --gradient-primary: linear-gradient(135deg, #0066ff 0%, #00d4ff 100%);
  --gradient-dark: linear-gradient(135deg, #0a0f1a 0%, #1e293b 100%);
  --gradient-light: linear-gradient(135deg, #f8fafb 0%, #ffffff 100%);

  /* 字体 */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;

  /* 间距 */
  --spacing-xs: 0.5rem;
  --spacing-sm: 1rem;
  --spacing-md: 2rem;
  --spacing-lg: 3rem;
  --spacing-xl: 5rem;
  --spacing-xxl: 8rem;

  /* 圆角 */
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 24px;

  /* 阴影 */
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.08);
  --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.12);
  --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.16);
  --shadow-xl: 0 16px 64px rgba(0, 0, 0, 0.2);
}

/* 基础字体设置 */
body {
  font-family: var(--font-primary);
  font-size: 16px;
  line-height: 1.6;
  color: var(--text-primary);
  background: var(--bg-white);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 容器样式 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 导航栏样式 */
.navbar {
  position: fixed;
  top: 0 !important;
  left: 0;
  width: 100%;
  background: #fff;
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 9999;
  transition: all 0.3s ease;
  margin: 0 !important;
  padding: 0;
}

.navbar.scrolled {
  background: #fff;
  backdrop-filter: blur(30px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.nav-container {
  max-width: 95%;
  margin: 0 auto;
  padding: 0 max(3%, 30px);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 85px;
}

.nav-logo {
  display: flex;
  align-items: center;
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--primary-blue);
  text-decoration: none;
}

.logo-image {
  width: 110px;
  height: 110px;
  object-fit: contain;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.logo-image:hover {
  transform: scale(1.05);
}

.nav-menu {
  display: flex;
  list-style: none;
  align-items: center;
  gap: 20px;
}

.nav-item {
  position: relative;
}

/* 导航链接统一样式 */
.nav-link {
  text-decoration: none;
  font-weight: 500;
  font-size: 1rem !important;
  transition: all 0.3s ease;
  padding: 0.6rem 1.2rem;
  border-radius: var(--radius-sm);
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #1a1a1a;
  text-shadow: none;
}

.navbar.scrolled .nav-link {
  color: #1a1a1a;
  text-shadow: none;
}

.nav-link:hover,
.nav-link.active {
  color: #00d4ff;
  background: rgba(255, 255, 255, 0.1);
}

.navbar.scrolled .nav-link:hover,
.navbar.scrolled .nav-link.active {
  color: var(--primary-blue);
  background: rgba(0, 102, 255, 0.05);
}

/* 下拉菜单样式 */
.dropdown {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background: #ffffff;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  padding: 8px 0;
  min-width: 180px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  z-index: 1000;
  border: 1px solid #e5e7eb;
  display: none !important;
}

.dropdown:hover .dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  display: block !important;
}

.dropdown-item {
  display: block;
  padding: 10px 16px;
  color: #374151;
  text-decoration: none;
  font-size: 0.9rem;
  transition: all 0.2s ease;
  border-radius: 4px;
  margin: 0 8px;
}

.dropdown-item:hover {
  background: #f3f4f6;
  color: var(--primary-blue);
}

.dropdown-item.active {
  background: rgba(0, 102, 255, 0.1);
  color: var(--primary-blue);
}

/* 移动端菜单切换按钮 */
.nav-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
  padding: 5px;
}

.nav-toggle span {
  width: 25px;
  height: 3px;
  background: var(--text-primary);
  margin: 3px 0;
  transition: 0.3s;
  border-radius: 2px;
}

/* 导航栏移动端优化 */
@media (max-width: 768px) {
  .nav-container {
    padding: 0;
    height: 75px;
  }

  .logo-image {
    width: 90px;
    height: 90px;
  }

  .nav-menu {
    position: fixed;
    top: 75px;
    left: -100%;
    width: 100%;
    height: calc(100vh - 75px);
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(30px);
    flex-direction: column;
    padding: 2rem 0;
    transition: all 0.3s ease;
    z-index: 1000;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
  }

  .nav-menu.active {
    left: 0;
  }

  .nav-item {
    margin: 0.5rem 0;
    width: 100%;
    text-align: center;
  }

  .nav-link {
    font-size: 1rem !important;
    padding: 1.2rem 2rem;
    display: block;
    border-radius: 10px;
    margin: 0 1.5rem;
    transition: all 0.3s ease;
    font-weight: 500;
  }

  .nav-toggle {
    display: flex;
  }

  .dropdown-menu {
    position: static;
    opacity: 1;
    visibility: visible;
    transform: none;
    box-shadow: none;
    border: none;
    background: transparent;
    padding: 0;
    margin-top: 10px;
  }

  .dropdown-item {
    margin: 5px 1.5rem;
    padding: 8px 16px;
    background: rgba(0, 102, 255, 0.05);
    border-radius: 8px;
  }
}

/* 毛玻璃球动画效果 */
.glass-orb {
  position: absolute;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(99, 102, 241, 0.15) 0%, rgba(139, 92, 246, 0.1) 50%, transparent 70%);
  backdrop-filter: blur(20px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  animation-duration: 20s;
  animation-iteration-count: infinite;
  animation-timing-function: ease-in-out;
  pointer-events: none;
  z-index: 1;
}

.orb-1 {
  width: 300px;
  height: 300px;
  top: 10%;
  left: 5%;
  animation-name: floatOrb;
  animation-delay: 0s;
}

.orb-2 {
  width: 200px;
  height: 200px;
  top: 60%;
  right: 10%;
  animation-name: floatOrb;
  animation-delay: 5s;
}

.orb-3 {
  width: 250px;
  height: 250px;
  bottom: 20%;
  left: 15%;
  animation-name: floatOrb;
  animation-delay: 10s;
}

.orb-4 {
  width: 180px;
  height: 180px;
  top: 30%;
  right: 25%;
  animation-name: floatOrb;
  animation-delay: 15s;
}

/* 毛玻璃球动画 */
@keyframes floatOrb {
  0%, 100% {
    transform: translate(0, 0) scale(1);
    opacity: 0.6;
  }
  25% {
    transform: translate(30px, -20px) scale(1.1);
    opacity: 0.8;
  }
  50% {
    transform: translate(-20px, 30px) scale(0.9);
    opacity: 0.7;
  }
  75% {
    transform: translate(40px, 10px) scale(1.05);
    opacity: 0.9;
  }
}

/* 通用按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 12px 24px;
  border: none;
  border-radius: var(--radius-md);
  font-size: 1rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  gap: 8px;
}

.btn-primary {
  background: var(--gradient-primary);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary {
  background: white;
  color: var(--primary-blue);
  border: 2px solid var(--border-light);
}

.btn-secondary:hover {
  background: var(--bg-light);
  transform: translateY(-2px);
}

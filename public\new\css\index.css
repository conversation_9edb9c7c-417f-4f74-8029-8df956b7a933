/* index.html 专用样式 */

/* 第三阶段指标横向并列显示 - 靠左下角 */
.hero-third-layout {
  position: absolute;
  bottom: 20px;
  left: 60px;
  width: auto;
}

.hero-third-header {
  margin-bottom: 80px;
}

.hero-third-stats .stat-group {
  display: flex !important;
  flex-direction: row !important;
  justify-content: flex-start;
  align-items: center;
  gap: 60px;
  flex-wrap: nowrap !important;
}

.hero-third-stats .stat-item-third {
  text-align: left;
  min-width: 120px;
  flex: 0 0 auto;
  display: inline-block;
}

.hero-third-stats .stat-number-container {
  display: flex;
  align-items: baseline;
  justify-content: flex-start;
  gap: 3px;
}

.hero-third-stats .stat-number-static {
  font-size: 4rem;
  font-weight: bold;
  color: white;
  line-height: 1;
}

.hero-third-stats .stat-unit {
  font-size: 1.5rem;
  color: white;
  opacity: 0.8;
}

.hero-third-stats .stat-label {
  margin-top: 8px;
  font-size: 1rem;
  color: white !important;
  opacity: 0.9;
}

/* 首页视频背景区域 */
.hero-video-section {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  z-index: 1;
}

.hero-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: -1;
}

.hero-content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

/* 初始内容样式 */
.hero-initial-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 1;
  transition: all 0.8s ease;
}

.hero-initial-content.fade-out {
  opacity: 0;
}

.hero-text {
  text-align: center;
  color: white;
  z-index: 3;
}

.hero-title {
  font-size: 4rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
  font-size: 1.5rem;
  margin-bottom: 2rem;
  opacity: 0.9;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.hero-logo {
  margin-top: 2rem;
}

.company-logo {
  max-width: 400px;
  height: auto;
  opacity: 0.9;
}

/* 滚动后内容样式 */
.hero-scroll-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.8s ease;
}

.hero-scroll-content.fade-in {
  opacity: 1;
  transform: translateY(0);
}

.hero-scroll-content.slide-out-up {
  opacity: 0;
  transform: translateY(-50px);
}

.hero-scroll-content.slide-out-down {
  opacity: 0;
  transform: translateY(50px);
}

.hero-split-layout {
  display: flex;
  width: 100%;
  max-width: 1200px;
  align-items: center;
  gap: 4rem;
  padding: 0 2rem;
}

.hero-left {
  flex: 1;
  color: white;
}

.hero-left-text {
  font-size: 3rem;
  font-weight: 700;
  line-height: 1.2;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-left-line {
  margin-bottom: 0.5rem;
}

.hero-right {
  flex: 1;
  color: white;
}

.hero-right-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-right-description {
  font-size: 1.1rem;
  line-height: 1.6;
  opacity: 0.9;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.hero-right-description p {
  margin-bottom: 1rem;
}

/* 第三模块内容样式 */
.hero-third-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: flex-end;
  justify-content: flex-start;
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.8s ease;
  padding: 2rem;
}

.hero-third-content.fade-in {
  opacity: 1;
  transform: translateY(0);
}

.hero-third-content.slide-out-down {
  opacity: 0;
  transform: translateY(50px);
}

.hero-third-title {
  font-size: 3rem;
  font-weight: 700;
  color: white;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-third-subtitle {
  font-size: 1.2rem;
  color: white;
  opacity: 0.9;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

/* 滚动进度指示器 */
.scroll-progress-indicator {
  position: absolute;
  bottom: 30px;
  right: 30px;
  width: 200px;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  overflow: hidden;
}

.progress-bar {
  width: 100%;
  height: 100%;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #00d4ff, #0066ff);
  width: 0%;
  transition: width 0.3s ease;
  border-radius: 2px;
}

/* 滚动指示 */
.scroll-indicator {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  color: white;
  opacity: 0.8;
  animation: bounce 2s infinite;
}

.scroll-indicator span {
  display: block;
  margin-bottom: 10px;
  font-size: 0.9rem;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.scroll-arrow {
  font-size: 1.5rem;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateX(-50%) translateY(0);
  }
  40% {
    transform: translateX(-50%) translateY(-10px);
  }
  60% {
    transform: translateX(-50%) translateY(-5px);
  }
}

/* 第一屏占位空间 */
.hero-spacer {
  height: 100vh;
  width: 100%;
}

/* 解决方案区域 */
.solutions-section {
  position: relative;
  padding: 100px 0;
  background: #ffffff;
  overflow: hidden;
}

.dynamic-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.solutions-container {
  position: relative;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  z-index: 2;
}

.solutions-header {
  text-align: center;
  margin-bottom: 4rem;
}

.solutions-main-title {
  font-size: 3.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 1.5rem;
}

.solutions-subtitle {
  font-size: 1.2rem;
  color: var(--text-secondary);
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.6;
}

.architecture-image-container {
  position: relative;
  text-align: center;
  margin-top: 3rem;
}

.architecture-img {
  max-width: 100%;
  height: auto;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
}

/* 时间线区域 */
.timeline-section {
  position: relative;
  padding: 100px 0;
  background: #f8fafb;
  overflow: hidden;
}

.timeline-container {
  position: relative;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  z-index: 2;
}

.timeline-header {
  text-align: center;
  margin-bottom: 4rem;
}

.timeline-content {
  position: relative;
}

.timeline-track {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.timeline-items-container {
  display: flex;
  gap: 2rem;
  padding: 2rem 0;
  overflow-x: auto;
  scroll-behavior: smooth;
}

.timeline-item {
  flex: 0 0 auto;
  width: 320px;
  position: relative;
}

.timeline-marker {
  width: 20px;
  height: 20px;
  background: var(--primary-blue);
  border-radius: 50%;
  margin: 0 auto 1rem;
  box-shadow: 0 0 0 4px rgba(0, 102, 255, 0.2);
}

.timeline-card {
  background: white;
  padding: 2rem;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
}

.timeline-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.timeline-date {
  font-size: 0.9rem;
  color: var(--primary-blue);
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.timeline-title {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.timeline-description {
  font-size: 0.95rem;
  color: var(--text-secondary);
  line-height: 1.5;
}

/* 合作机构轮播 */
.partners-section {
  margin-top: 4rem;
  padding: 3rem 0;
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
}

.partners-carousel {
  overflow: hidden;
  position: relative;
}

.partners-track {
  display: flex;
  animation: scroll-partners 30s linear infinite;
  gap: 3rem;
  align-items: center;
}

.partner-item {
  flex: 0 0 auto;
  width: 200px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;
}

.partner-item:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-md);
}

.partner-item img {
  max-width: 80%;
  max-height: 80%;
  object-fit: contain;
  filter: grayscale(100%);
  transition: filter 0.3s ease;
}

.partner-item:hover img {
  filter: grayscale(0%);
}

@keyframes scroll-partners {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

/* 联系区域 */
.contact-section {
  position: relative;
  height: 100vh;
  background: linear-gradient(135deg, #0a0f1a 0%, #1e293b 100%);
  overflow: hidden;
}

.contact-background {
  position: relative;
  width: 100%;
  height: 100%;
  background-image: url('../assets/first_page/contact_bg.jpg');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
}

.contact-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  z-index: 1;
}

.contact-content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 4rem;
  z-index: 2;
}

.contact-message {
  flex: 1;
  color: white;
}

.contact-title {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.2;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.title-line {
  margin-bottom: 0.5rem;
}

.partner-text {
  color: var(--accent-cyan);
  text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
}

.contact-info-section {
  position: relative;
  flex: 0 0 auto;
  margin-left: 2rem;
}

.sliding-line {
  position: absolute;
  top: 0;
  left: -100px;
  width: 200px;
  height: 2px;
  background: linear-gradient(90deg, transparent, #00d4ff, transparent);
  animation: slide-line 3s ease-in-out infinite;
}

@keyframes slide-line {
  0%, 100% {
    transform: translateX(-100px);
    opacity: 0;
  }
  50% {
    transform: translateX(100px);
    opacity: 1;
  }
}

.contact-details-new {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-lg);
  padding: 2rem;
  text-align: center;
  color: white;
}

.contact-slogan {
  font-size: 1.2rem;
  margin-bottom: 1rem;
  opacity: 0.9;
}

.contact-person-info {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--accent-cyan);
}

.contact-qrcode-new {
  margin-top: 1rem;
}

.qrcode-full {
  width: 120px;
  height: 120px;
  border-radius: var(--radius-md);
  transition: all 0.3s ease;
}

.qrcode-full:hover {
  transform: scale(1.05);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

/* 响应式设计 - 移动端适配 */
@media (max-width: 768px) {
  .hero-third-layout {
    bottom: 40px;
    left: 30px;
  }

  .hero-third-stats .stat-group {
    gap: 40px;
  }

  .hero-third-stats .stat-number-static {
    font-size: 3rem;
  }

  .hero-third-stats .stat-unit {
    font-size: 1.2rem;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.2rem;
  }

  .hero-split-layout {
    flex-direction: column;
    gap: 2rem;
    text-align: center;
  }

  .hero-left-text {
    font-size: 2rem;
  }

  .hero-right-title {
    font-size: 2rem;
  }

  .hero-right-description {
    font-size: 1rem;
  }

  .hero-third-title {
    font-size: 2rem;
  }

  .hero-third-subtitle {
    font-size: 1rem;
  }

  .solutions-main-title {
    font-size: 2.2rem;
    margin: 0 auto;
    padding: 0 15px;
  }

  .solutions-subtitle {
    font-size: 1rem;
    white-space: normal;
    padding: 0 10px;
  }

  .architecture-img {
    width: 85%;
    max-height: 55vh;
  }

  .timeline-items-container {
    flex-direction: column;
    overflow-y: auto;
    overflow-x: hidden;
    max-height: 400px;
  }

  .timeline-item {
    width: 100%;
    margin-bottom: 1rem;
  }

  .partners-track {
    animation-duration: 20s;
  }

  .partner-item {
    width: 150px;
    height: 80px;
  }

  .contact-content {
    flex-direction: column;
    padding: 2rem;
    text-align: center;
    gap: 2rem;
  }

  .contact-title {
    font-size: 2.5rem;
  }

  .contact-info-section {
    margin-left: 0;
  }

  .scroll-progress-indicator {
    bottom: 20px;
    right: 20px;
    width: 150px;
  }

  .scroll-indicator {
    bottom: 20px;
  }

  .orb-1,
  .orb-2,
  .orb-3,
  .orb-4 {
    transform: scale(0.7);
  }
}

/* cases.html 专用样式 */

/* 案例页面主容器 */
.cases-main {
  padding-top: 80px;
}

/* 案例页面头部 */
.cases-hero {
  padding: 80px 0 60px;
  background: #ffffff;
  position: relative;
  overflow: hidden;
}

.cases-hero .container {
  max-width: 95%;
  margin: 0 auto;
  padding: 0 max(3%, 30px);
}

.cases-header {
  position: relative;
  z-index: 2;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 80px;
  min-height: 400px;
}

.header-left {
  flex: 0 0 auto;
  min-width: 350px;
}

.cases-title {
  font-size: 8rem;
  font-weight: 900;
  color: #f0f0f0;
  text-transform: uppercase;
  letter-spacing: -0.02em;
  line-height: 0.9;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, #ddd6fe 0%, #8b5cf6 50%, #7c3aed 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.header-right {
  flex: 1;
  margin-left: 100px;
}

.cases-info {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(226, 232, 240, 0.3);
  border-radius: 24px;
  padding: 40px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
}

.cases-info-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 20px;
  line-height: 1.2;
}

.cases-description {
  font-size: 1.2rem;
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0;
}

.highlight-text {
  color: #7c3aed;
  font-weight: 600;
}

/* 浮动的毛玻璃球 - 紫色主题 */
.glass-ball-purple-1 {
  position: absolute;
  top: 15%;
  left: 8%;
  width: 250px;
  height: 250px;
  background: radial-gradient(circle, rgba(139, 92, 246, 0.12) 0%, rgba(124, 58, 237, 0.08) 50%, transparent 70%);
  backdrop-filter: blur(20px);
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.3);
  animation: floatPurpleGlassBall1 15s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
}

.glass-ball-purple-2 {
  position: absolute;
  top: 45%;
  right: 12%;
  width: 180px;
  height: 180px;
  background: radial-gradient(circle, rgba(124, 58, 237, 0.1) 0%, rgba(109, 40, 217, 0.06) 50%, transparent 70%);
  backdrop-filter: blur(15px);
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.25);
  animation: floatPurpleGlassBall2 18s ease-in-out infinite reverse;
  pointer-events: none;
  z-index: 1;
}

.glass-ball-purple-3 {
  position: absolute;
  bottom: 20%;
  left: 15%;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(221, 214, 254, 0.08) 0%, rgba(139, 92, 246, 0.05) 50%, transparent 70%);
  backdrop-filter: blur(18px);
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: floatPurpleGlassBall3 20s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
}

/* 紫色毛玻璃球动画 */
@keyframes floatPurpleGlassBall1 {
  0%, 100% { transform: translate(0, 0) rotate(0deg); }
  25% { transform: translate(30px, -20px) rotate(90deg); }
  50% { transform: translate(-20px, 30px) rotate(180deg); }
  75% { transform: translate(40px, 10px) rotate(270deg); }
}

@keyframes floatPurpleGlassBall2 {
  0%, 100% { transform: translate(0, 0) scale(1); }
  33% { transform: translate(-25px, 20px) scale(1.1); }
  66% { transform: translate(35px, -15px) scale(0.9); }
}

@keyframes floatPurpleGlassBall3 {
  0%, 100% { transform: translate(0, 0) rotate(0deg) scale(1); }
  50% { transform: translate(-30px, -25px) rotate(180deg) scale(1.05); }
}

/* 案例展示区域 */
.cases-showcase {
  padding: 80px 0;
  background: #f8fafb;
}

.cases-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 40px;
  margin-top: 40px;
}

.case-card {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid rgba(226, 232, 240, 0.3);
  position: relative;
}

.case-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.12);
}

.case-image {
  position: relative;
  height: 280px;
  overflow: hidden;
  background: linear-gradient(135deg, #ddd6fe 0%, #8b5cf6 100%);
}

.case-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.case-card:hover .case-img {
  transform: scale(1.05);
}

.case-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, rgba(124, 58, 237, 0.1) 0%, rgba(139, 92, 246, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.case-card:hover .case-overlay {
  opacity: 1;
}

.case-category-badge {
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(124, 58, 237, 0.9);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  backdrop-filter: blur(10px);
}

.case-content {
  padding: 30px;
}

.case-title {
  font-size: 1.6rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 16px;
  line-height: 1.3;
}

.case-client {
  display: inline-block;
  background: rgba(124, 58, 237, 0.1);
  color: #7c3aed;
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  margin-bottom: 16px;
}

.case-summary {
  font-size: 1rem;
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 20px;
}

.case-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 20px;
}

.case-tag {
  background: rgba(124, 58, 237, 0.05);
  color: #7c3aed;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.case-action {
  padding-top: 20px;
  border-top: 1px solid rgba(226, 232, 240, 0.5);
}

.case-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background: linear-gradient(135deg, #7c3aed 0%, #8b5cf6 100%);
  color: white;
  text-decoration: none;
  border-radius: 12px;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.case-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(124, 58, 237, 0.3);
}

.case-btn i {
  transition: transform 0.3s ease;
}

.case-btn:hover i {
  transform: translateX(3px);
}

/* 案例筛选器 */
.cases-filters {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 40px;
  flex-wrap: wrap;
}

.filter-btn {
  padding: 10px 20px;
  border: 2px solid rgba(124, 58, 237, 0.2);
  background: white;
  color: var(--text-secondary);
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  font-weight: 500;
}

.filter-btn:hover,
.filter-btn.active {
  background: #7c3aed;
  color: white;
  border-color: #7c3aed;
}

/* 案例统计 */
.cases-stats {
  padding: 80px 0;
  background: linear-gradient(135deg, #ddd6fe 0%, #8b5cf6 100%);
  color: white;
  text-align: center;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 40px;
  margin-top: 40px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 3rem;
  font-weight: 900;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
}

.stat-label {
  font-size: 1.1rem;
  opacity: 0.9;
  font-weight: 500;
}

/* 响应式设计 - 移动端适配 */
@media (max-width: 768px) {
  .cases-hero {
    padding: 60px 0 40px;
  }

  .header-content {
    flex-direction: column;
    gap: 40px;
    min-height: auto;
    text-align: center;
  }

  .header-left {
    min-width: auto;
    width: 100%;
  }

  .cases-title {
    font-size: 4rem;
  }

  .header-right {
    margin-left: 0;
    width: 100%;
  }

  .cases-info {
    padding: 30px 20px;
  }

  .cases-info-title {
    font-size: 2rem;
  }

  .cases-description {
    font-size: 1rem;
  }

  .cases-grid {
    grid-template-columns: 1fr;
    gap: 30px;
    margin-top: 30px;
  }

  .case-card {
    border-radius: 16px;
  }

  .case-image {
    height: 220px;
  }

  .case-content {
    padding: 20px;
  }

  .case-title {
    font-size: 1.4rem;
  }

  .cases-filters {
    gap: 10px;
    margin-bottom: 30px;
  }

  .filter-btn {
    padding: 8px 16px;
    font-size: 0.85rem;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
  }

  .stat-number {
    font-size: 2.5rem;
  }

  .stat-label {
    font-size: 1rem;
  }

  /* 隐藏部分毛玻璃球以减少移动端视觉干扰 */
  .glass-ball-purple-3 {
    display: none;
  }

  .glass-ball-purple-1,
  .glass-ball-purple-2 {
    transform: scale(0.6);
  }
}

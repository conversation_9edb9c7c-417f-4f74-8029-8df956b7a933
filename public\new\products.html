<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品中心 - 银河星尘</title>
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/products.css">
    <link href="css/all.min.css" rel="stylesheet">
    <script src="js/gsap.min.js"></script>
    <script src="js/ScrollTrigger.min.js"></script>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <img src="company_logo.png" alt="银河星尘" class="logo-image">
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="index.html" class="nav-link">首页</a>
                </li>
                <li class="nav-item">
                    <a href="products.html" class="nav-link active">产品中心</a>
                </li>
                <li class="nav-item dropdown">
                    <a href="scenarios.html" class="nav-link">应用场景</a>
                    <div class="dropdown-menu">
                        <a href="scenarios.html" class="dropdown-item">工地监测</a>
                        <a href="scenarios.html" class="dropdown-item">公园管理</a>
                        <a href="scenarios.html" class="dropdown-item">垃圾场治理</a>
                        <a href="scenarios.html" class="dropdown-item">学校防护</a>
                        <a href="scenarios.html" class="dropdown-item">医院环境</a>
                        <a href="scenarios.html" class="dropdown-item">园区服务</a>
                        <a href="scenarios.html" class="dropdown-item">贸易市场</a>
                        <a href="scenarios.html#cases" class="dropdown-item">落地案例</a>
                    </div>
                </li>
                <li class="nav-item">
                    <a href="news.html" class="nav-link">新闻中心</a>
                </li>
                <li class="nav-item">
                    <a href="company.html" class="nav-link">企业信息</a>
                </li>
            </ul>
            <div class="nav-toggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <main class="products-main">
        <!-- 产品中心头部 -->
        <section class="products-hero slide-in-scale">
            <!-- 浮动的毛玻璃球 -->
            <div class="glass-ball-2"></div>
            <div class="glass-ball-3"></div>
            <div class="glass-ball-4"></div>
            <div class="glass-ball-5"></div>
            <div class="glass-ball-6"></div>

            <div class="container">
                <div class="products-header">
                    <div class="header-content">
                        <div class="header-left">
                            <h1 class="products-title">Products</h1>
                        </div>
                        <div class="header-right">
                        <div class="solutions-info">
                            <h2 class="solutions-title">解决方案</h2>
                            <p class="solutions-description">
                                    银河星尘采用最先进的技术手段，融合病媒AI大模型及智能物联网(IoT)设备，结合地理信息系统(GIS)、深度学习及数据分析技术，构建了一套全面的病媒监测预警系统。<strong class="highlight-text">该系统能够实时监测病媒生物的分布情况、活动规律和密度变化，为用户提供精准的预警服务。</strong>
                            </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 产品展示 -->
        <section class="products-showcase page-section">
            <div class="container">
                <div class="products-grid">
                    <!-- 产品1：星尘X1 -->
                    <div class="product-card" data-product="x1">
                        <div class="product-image">
                            <img src="assets/product_center/device.png" alt="星尘X1" class="product-img">
                        </div>
                        <div class="product-content">
                            <div class="product-info">
                                <span class="product-category">智能硬件</span>
                                <h3 class="product-title">星尘X1</h3>
                                <p class="product-summary">自主研发的病媒AI智能设备，集高效捕蚊与智能监测于一体</p>
                                </div>
                            <div class="product-expand">
                                <button class="expand-btn" onclick="toggleExpand(this)">
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                            </div>
                        </div>
                        <div class="product-details">
                            <p class="product-description">
                                自主研发的病媒AI智能设备，集高效捕蚊与智能监测于一体。搭载高清摄像头，弱光下也能清晰成像；配备太阳能供电系统，适应恶劣天气，实现持久离网运行。创新的二氧化碳组件精准吸引目标，高精度传感器灵敏捕捉微小变化。采用环保物理技术自动清理蚊虫，无需化学药剂。内置智能控制器，通过4G/5G实时传输数据，自动化管理捕蚊、识别、清理全流程解决方案。
                            </p>
                        </div>
                    </div>

                    <!-- 产品2：星尘AI+病媒监测平台 -->
                    <div class="product-card" data-product="platform">
                        <div class="product-image">
                            <img src="assets/product_center/platform.png" alt="星尘AI+病媒监测平台" class="product-img">
                        </div>
                        <div class="product-content">
                            <div class="product-info">
                                <span class="product-category">监测平台</span>
                                <h3 class="product-title">星尘AI+病媒监测平台</h3>
                                <p class="product-summary">基于AI大模型的综合监测平台，提供实时监测、数据分析、预警预测、决策支持等服务</p>
                                </div>
                            <div class="product-expand">
                                <button class="expand-btn" onclick="toggleExpand(this)">
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                            </div>
                        </div>
                        <div class="product-details">
                            <p class="product-description">
                                星尘AI病媒监测平台运用前沿人工智能技术，实现对区域内及设备的蚊虫动态进行全天候智能监测与分析。平台支持按蚊种、区域、生境、设备维度，从密度、数量、增长率等多个指标进行精细化分析，提供定制化的监测报告下载功能。相比传统人工监测，该平台结合实时预警系统，能够提前预测蚊虫爆发风险，实现全方位、多维度的智能防控，大幅提升监测效率和预警准确性。
                            </p>
                        </div>
                    </div>

                    <!-- 产品3：星小尘模型 -->
                    <div class="product-card" data-product="assistant">
                        <div class="product-image">
                            <img src="assets/product_center/module.png" alt="星小尘模型" class="product-img">
                        </div>
                        <div class="product-content">
                            <div class="product-info">
                                <span class="product-category">大模型</span>
                                <h3 class="product-title">星小尘模型</h3>
                                <p class="product-summary">专业化病媒防控大模型，提供智能问答与决策支持服务</p>
                                </div>
                            <div class="product-expand">
                                <button class="expand-btn" onclick="toggleExpand(this)">
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                            </div>
                        </div>
                        <div class="product-details">
                            <p class="product-description">
                                星小尘模型是专门针对病媒防控领域训练的专业大模型，具备丰富的病媒生物学知识和防控经验。能够为用户提供专业的病媒识别、防控策略制定、风险评估等智能咨询服务。通过深度学习病媒防控领域的专业知识，模型能够精准回答各类病媒相关问题，协助制定科学的防控方案，成为病媒防控工作者的专业AI工具。
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 技术与服务优势 -->
        <section class="advantages-section slide-in-left">
            <div class="container">
                <h2 class="section-title">技术与服务优势</h2>
                <div class="advantages-layout">
                    <!-- 技术优势 -->
                    <div class="tech-advantages-area">
                        <h3 class="subsection-title">技术优势</h3>
                        <div class="tech-grid">
                            <div class="tech-item">
                                <div class="tech-icon">
                            <i class="fas fa-brain"></i>
                        </div>
                                <div class="tech-content">
                                    <h4>AI大模型技术</h4>
                        <p>自研AI算法，识别准确率达95%</p>
                    </div>
                            </div>
                            <div class="tech-item">
                                <div class="tech-icon">
                            <i class="fas fa-network-wired"></i>
                        </div>
                                <div class="tech-content">
                                    <h4>物联网融合</h4>
                        <p>IoT设备与云端平台无缝连接</p>
                    </div>
                            </div>
                            <div class="tech-item">
                                <div class="tech-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                                <div class="tech-content">
                                    <h4>大数据分析</h4>
                        <p>实时数据处理与智能预警</p>
                    </div>
                            </div>
                            <div class="tech-item">
                                <div class="tech-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                                <div class="tech-content">
                                    <h4>7×24小时监测</h4>
                        <p>全天候自动化监测服务</p>
                    </div>
                </div>
            </div>
                    </div>

        <!-- 服务优势 -->
                    <div class="service-advantages-area">
                        <h3 class="subsection-title">服务优势</h3>
                        <div class="service-grid">
                    <!-- 病媒生物监测服务 -->
                    <div class="service-card">
                        <div class="service-header">
                            <div class="service-icon">
                                <i class="fas fa-search-plus"></i>
                            </div>
                                    <h4 class="service-title">病媒生物监测服务</h4>
                        </div>
                        <div class="service-content">
                            <p class="service-description">
                                更精准、全面的监测鼠类、蚊虫的种类、数量、分布密度及监测报告等数据
                            </p>
                            <div class="service-features">
                                <div class="feature-point">
                                    <i class="fas fa-check"></i>
                                    <span>精准物种识别</span>
                                </div>
                                <div class="feature-point">
                                    <i class="fas fa-check"></i>
                                    <span>实时数量统计</span>
                                </div>
                                <div class="feature-point">
                                    <i class="fas fa-check"></i>
                                    <span>分布密度分析</span>
                                </div>
                                <div class="feature-point">
                                    <i class="fas fa-check"></i>
                                    <span>详细监测报告</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 疾病预测服务 -->
                    <div class="service-card">
                        <div class="service-header">
                            <div class="service-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                                    <h4 class="service-title">疾病预测服务</h4>
                        </div>
                        <div class="service-content">
                            <p class="service-description">
                                环境疾病爆发风险预测、病媒监测、活动及控制效果预测、公共发布数据风险、趋势预测等
                            </p>
                            <div class="service-features">
                                <div class="feature-point">
                                    <i class="fas fa-check"></i>
                                    <span>疾病风险预测</span>
                                </div>
                                <div class="feature-point">
                                    <i class="fas fa-check"></i>
                                    <span>控制效果评估</span>
                                </div>
                                <div class="feature-point">
                                    <i class="fas fa-check"></i>
                                    <span>公共数据发布</span>
                                </div>
                                <div class="feature-point">
                                    <i class="fas fa-check"></i>
                                    <span>趋势分析预测</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <div id="footer-container"></div>
    <script src="js/footer.js"></script>

    <script src="js/common.js"></script>
    <script>
        // 页面整体滑动动画初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 创建Intersection Observer来监控元素进入视口
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        // 添加动画类
                        entry.target.classList.add('animate-in');
                    } else {
                        // 元素离开视口时移除动画类（支持反向动画）
                        entry.target.classList.remove('animate-in');
                    }
                });
            }, {
                threshold: 0.1, // 当元素10%可见时触发
                rootMargin: '0px 0px -50px 0px' // 提前50px触发动画
            });

            // 为所有需要动画的元素添加观察
            const animatedElements = document.querySelectorAll('.page-section, .slide-in-left, .slide-in-right, .slide-in-scale');
            animatedElements.forEach(el => {
                observer.observe(el);
            });
        });

        // 产品展开/收起功能
        function toggleExpand(button) {
            const productCard = button.closest('.product-card');
            const details = productCard.querySelector('.product-details');
            const icon = button.querySelector('i');

            if (details.style.display === 'none') {
                details.style.display = 'block';
                icon.style.transform = 'rotate(180deg)';
                gsap.fromTo(details,
                    { opacity: 0, height: 0 },
                    { opacity: 1, height: 'auto', duration: 0.3, ease: "power2.out" }
                );
            } else {
                gsap.to(details, {
                    opacity: 0,
                    height: 0,
                    duration: 0.3,
                    ease: "power2.out",
                    onComplete: () => {
                        details.style.display = 'none';
                    }
                });
                icon.style.transform = 'rotate(0deg)';
            }
        }

        // 产品页面专用脚本
        document.addEventListener('DOMContentLoaded', function() {
            // 产品卡片悬停效果
            const productCards = document.querySelectorAll('.product-card');
            productCards.forEach(card => {
                card.addEventListener('mouseenter', () => {
                    gsap.to(card, {
                        y: -5,
                        scale: 1.01,
                        duration: 0.3,
                        ease: "power2.out"
                    });
                });

                card.addEventListener('mouseleave', () => {
                    gsap.to(card, {
                        y: 0,
                        scale: 1,
                        duration: 0.3,
                        ease: "power2.out"
                    });
                });
            });

            // 页面加载动画
            gsap.fromTo('.products-title',
                { x: -50, opacity: 0 },
                { x: 0, opacity: 1, duration: 0.8, ease: "power2.out" }
            );

            gsap.fromTo('.products-subtitle',
                { x: -30, opacity: 0 },
                { x: 0, opacity: 1, duration: 0.8, delay: 0.1, ease: "power2.out" }
            );

            gsap.fromTo('.solutions-info',
                { x: 50, opacity: 0 },
                { x: 0, opacity: 1, duration: 0.8, delay: 0.2, ease: "power2.out" }
            );

            gsap.fromTo('.product-card',
                { y: 50, opacity: 0 },
                { y: 0, opacity: 1, duration: 0.6, stagger: 0.2, delay: 0.4, ease: "power2.out" }
            );

            // 技术与服务优势滑动动画
            // 标题动画
            gsap.to('.advantages-section .section-title', {
                opacity: 1,
                y: 0,
                duration: 0.8,
                ease: "power2.out",
                scrollTrigger: {
                    trigger: '.advantages-section .section-title',
                    start: 'top 80%',
                    end: 'bottom 20%',
                    toggleActions: 'play none none reverse'
                }
            });

            // 技术优势区域从左滑入
            gsap.to('.tech-advantages-area', {
                opacity: 1,
                x: 0,
                duration: 0.8,
                ease: "power2.out",
                scrollTrigger: {
                    trigger: '.tech-advantages-area',
                    start: 'top 80%',
                    end: 'bottom 20%',
                    toggleActions: 'play none none reverse'
                }
            });

            // 服务优势区域从右滑入
            gsap.to('.service-advantages-area', {
                opacity: 1,
                x: 0,
                duration: 0.8,
                ease: "power2.out",
                scrollTrigger: {
                    trigger: '.service-advantages-area',
                    start: 'top 80%',
                    end: 'bottom 20%',
                    toggleActions: 'play none none reverse'
                }
            });

            // 技术项目逐个滑入
            gsap.to('.tech-item', {
                opacity: 1,
                y: 0,
                duration: 0.6,
                stagger: 0.2,
                ease: "power2.out",
                scrollTrigger: {
                    trigger: '.tech-grid',
                    start: 'top 75%',
                    end: 'bottom 25%',
                    toggleActions: 'play none none reverse'
                }
            });

            // 服务卡片逐个滑入
            gsap.to('.service-card', {
                opacity: 1,
                y: 0,
                duration: 0.6,
                stagger: 0.3,
                ease: "power2.out",
                scrollTrigger: {
                    trigger: '.service-grid',
                    start: 'top 75%',
                    end: 'bottom 25%',
                    toggleActions: 'play none none reverse'
                }
            });
        });
    </script>
</body>
</html>

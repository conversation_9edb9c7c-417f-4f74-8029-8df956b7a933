/* news-detail.html 专用样式 */

/* 新闻详情页面主容器 */
.news-detail-main {
  padding-top: 80px;
}

/* 新闻详情头部 */
.news-detail-hero {
  padding: 80px 0 60px;
  background: linear-gradient(135deg, #f8fafb 0%, #e2e8f0 100%);
  position: relative;
  overflow: hidden;
}

.news-detail-hero .container {
  max-width: 95%;
  margin: 0 auto;
  padding: 0 max(3%, 30px);
}

.news-detail-header {
  position: relative;
  z-index: 2;
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.news-breadcrumb {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-bottom: 30px;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.breadcrumb-link {
  color: #16a34a;
  text-decoration: none;
  transition: color 0.3s ease;
}

.breadcrumb-link:hover {
  color: #15803d;
}

.breadcrumb-separator {
  color: var(--text-light);
}

.news-detail-category {
  display: inline-block;
  background: rgba(22, 163, 74, 0.1);
  color: #16a34a;
  padding: 8px 20px;
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 20px;
}

.news-detail-title {
  font-size: 3rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 30px;
  line-height: 1.2;
}

.news-detail-meta {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 30px;
  margin-bottom: 40px;
  font-size: 0.95rem;
  color: var(--text-secondary);
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.meta-item i {
  color: #16a34a;
}

.news-detail-summary {
  font-size: 1.2rem;
  color: var(--text-secondary);
  line-height: 1.6;
  max-width: 700px;
  margin: 0 auto;
  font-style: italic;
}

/* 新闻详情内容区域 */
.news-detail-content {
  padding: 80px 0;
  background: white;
}

.content-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 2rem;
}

.news-detail-image {
  position: relative;
  margin-bottom: 40px;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.12);
}

.news-detail-img {
  width: 100%;
  height: 400px;
  object-fit: cover;
}

.image-caption {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  color: white;
  padding: 20px;
  font-size: 0.9rem;
  line-height: 1.4;
}

.news-content {
  font-size: 1.1rem;
  line-height: 1.8;
  color: var(--text-primary);
}

.news-content h2 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 40px 0 20px;
  line-height: 1.3;
}

.news-content h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 30px 0 15px;
  line-height: 1.3;
}

.news-content p {
  margin-bottom: 20px;
  text-align: justify;
}

.news-content ul,
.news-content ol {
  margin: 20px 0;
  padding-left: 30px;
}

.news-content li {
  margin-bottom: 10px;
}

.news-content blockquote {
  background: #f8fafb;
  border-left: 4px solid #16a34a;
  padding: 20px 30px;
  margin: 30px 0;
  font-style: italic;
  border-radius: 0 8px 8px 0;
}

.news-content img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  margin: 20px 0;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.highlight-box {
  background: linear-gradient(135deg, rgba(22, 163, 74, 0.05) 0%, rgba(34, 197, 94, 0.02) 100%);
  border: 1px solid rgba(22, 163, 74, 0.2);
  border-radius: 12px;
  padding: 25px;
  margin: 30px 0;
}

.highlight-box h4 {
  color: #16a34a;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 15px;
}

/* 新闻详情底部 */
.news-detail-footer {
  padding: 60px 0;
  background: #f8fafb;
  border-top: 1px solid rgba(226, 232, 240, 0.5);
}

.footer-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 2rem;
}

.news-tags {
  margin-bottom: 40px;
}

.tags-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 15px;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.news-tag {
  background: white;
  color: var(--text-secondary);
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.9rem;
  text-decoration: none;
  border: 1px solid rgba(226, 232, 240, 0.5);
  transition: all 0.3s ease;
}

.news-tag:hover {
  background: #16a34a;
  color: white;
  border-color: #16a34a;
}

.news-share {
  margin-bottom: 40px;
}

.share-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 15px;
}

.share-buttons {
  display: flex;
  gap: 15px;
}

.share-btn {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  color: white;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.share-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.share-btn.wechat {
  background: #07c160;
}

.share-btn.weibo {
  background: #e6162d;
}

.share-btn.qq {
  background: #12b7f5;
}

.share-btn.link {
  background: var(--text-secondary);
}

/* 相关新闻 */
.related-news {
  padding: 80px 0;
  background: white;
}

.related-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.related-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  text-align: center;
  margin-bottom: 50px;
}

.related-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
}

.related-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(226, 232, 240, 0.3);
  transition: all 0.3s ease;
}

.related-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.12);
}

.related-image {
  height: 200px;
  overflow: hidden;
}

.related-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.related-card:hover .related-img {
  transform: scale(1.05);
}

.related-content {
  padding: 25px;
}

.related-card-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 10px;
  line-height: 1.3;
}

.related-card-title a {
  color: inherit;
  text-decoration: none;
  transition: color 0.3s ease;
}

.related-card-title a:hover {
  color: #16a34a;
}

.related-date {
  font-size: 0.9rem;
  color: var(--text-light);
}

/* 响应式设计 - 移动端适配 */
@media (max-width: 768px) {
  .news-detail-hero {
    padding: 60px 0 40px;
  }

  .news-breadcrumb {
    flex-wrap: wrap;
    gap: 5px;
    font-size: 0.8rem;
  }

  .news-detail-title {
    font-size: 2rem;
    margin-bottom: 20px;
  }

  .news-detail-meta {
    flex-direction: column;
    gap: 15px;
    margin-bottom: 30px;
  }

  .news-detail-summary {
    font-size: 1rem;
  }

  .content-container {
    padding: 0 1rem;
  }

  .news-detail-img {
    height: 250px;
  }

  .image-caption {
    padding: 15px;
    font-size: 0.8rem;
  }

  .news-content {
    font-size: 1rem;
    line-height: 1.7;
  }

  .news-content h2 {
    font-size: 1.6rem;
    margin: 30px 0 15px;
  }

  .news-content h3 {
    font-size: 1.3rem;
    margin: 25px 0 12px;
  }

  .news-content blockquote {
    padding: 15px 20px;
    margin: 20px 0;
  }

  .highlight-box {
    padding: 20px 15px;
    margin: 20px 0;
  }

  .footer-container {
    padding: 0 1rem;
  }

  .tag-list {
    gap: 8px;
  }

  .news-tag {
    padding: 6px 12px;
    font-size: 0.8rem;
  }

  .share-buttons {
    gap: 12px;
  }

  .share-btn {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .related-container {
    padding: 0 1rem;
  }

  .related-title {
    font-size: 2rem;
    margin-bottom: 30px;
  }

  .related-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .related-image {
    height: 180px;
  }

  .related-content {
    padding: 20px;
  }

  .related-card-title {
    font-size: 1.1rem;
  }
}

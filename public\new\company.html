<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>银河星尘-领先的病媒生物智能监测领域服务商</title>
    <link rel="stylesheet" href="css/common.css" />
    <link rel="stylesheet" href="css/company.css" />
    <link href="css/all.min.css" rel="stylesheet" />
    <script src="js/gsap.min.js"></script>
    <script src="js/ScrollTrigger.min.js"></script>
  </head>
  <body>
    <!-- 导航栏 -->
    <nav class="navbar">
      <div class="nav-container">
        <div class="nav-logo">
          <img src="company_logo.png" alt="银河星尘" class="logo-image" />
        </div>
        <ul class="nav-menu">
          <li class="nav-item">
            <a href="index.html" class="nav-link">首页</a>
          </li>
          <li class="nav-item">
            <a href="products.html" class="nav-link">产品中心</a>
          </li>
          <li class="nav-item dropdown">
            <a href="scenarios.html" class="nav-link">应用场景</a>
            <div class="dropdown-menu">
              <a href="scenarios.html" class="dropdown-item">工地监测</a>
              <a href="scenarios.html" class="dropdown-item">公园管理</a>
              <a href="scenarios.html" class="dropdown-item">垃圾场治理</a>
              <a href="scenarios.html" class="dropdown-item">学校防护</a>
              <a href="scenarios.html" class="dropdown-item">医院环境</a>
              <a href="scenarios.html" class="dropdown-item">园区服务</a>
              <a href="scenarios.html" class="dropdown-item">贸易市场</a>
              <a href="scenarios.html#cases" class="dropdown-item">落地案例</a>
            </div>
          </li>
          <li class="nav-item">
            <a href="news.html" class="nav-link">新闻中心</a>
          </li>
          <li class="nav-item">
            <a href="company.html" class="nav-link active">企业信息</a>
          </li>
        </ul>
        <div class="nav-toggle">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>
    </nav>

    <main class="company-main" style="padding-top: 85px">
      <!-- 第一屏：公司信息 -->
      <section class="company-info-screen slide-in-left">
        <div class="info-background">
          <div class="container">
            <div class="info-content">
              <div class="info-left">
                <div class="brand-text">
                  <h1 class="brand-line-1">GALACTIC</h1>
                  <h1 class="brand-line-2">STARDUST</h1>
                  <h1 class="brand-line-3">病媒监测</h1>
                </div>
              </div>
              <div class="info-right">
                <h2 class="company-name">深圳银河星尘科技有限公司</h2>
                <div class="company-description">
                  <p>
                    成立于2024年8月，是一家<span class="highlight-text"
                      >领先的人工智能技术高科技公司</span
                    >。
                  </p>
                  <p>
                    汇聚来自<span class="highlight-text"
                      >人工智能、医疗健康</span
                    >及<span class="highlight-text">病媒生物</span
                    >三大领域的顶尖专家科研团队，与业内权威机构建立了稳固的战略合作关系。
                  </p>
                  <p>
                    公司依托强大的研发实力，运用先进的人工智能技术，成功研发出一系列创新、高效的技术解决方案及智能监测设备。通过深度融合AI大模型、智能物联网（IoT，Internet
                    of Things
                    ）硬件和大数据分析技术，为客户提供更全面的病媒监测、分析一体化服务。
                  </p>
                  <p>
                    我们致力于通过技术创新，保护人类健康，增强公共卫生防疫能力，推动人类社会的健康可持续发展。
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 第三屏：核心团队介绍 -->
      <section class="team-screen slide-in-right">
        <div class="team-background">
          <!-- 动态背景球 -->
          <div class="floating-ball ball-1"></div>
          <div class="floating-ball ball-2"></div>
          <div class="floating-ball ball-3"></div>
          <div class="floating-ball ball-4"></div>

          <div class="team-section-wrapper">
            <div class="team-header">
              <h2 class="team-title">核心团队</h2>
              <p class="team-subtitle">Core Team</p>
            </div>

            <!-- 横向滚动团队容器 -->
            <div class="team-carousel-container">
              <!-- 左右滑动按钮 -->
              <button class="team-nav-btn team-prev-btn" id="teamPrevBtn">
                <i class="fas fa-chevron-left"></i>
              </button>
              <button class="team-nav-btn team-next-btn" id="teamNextBtn">
                <i class="fas fa-chevron-right"></i>
              </button>

              <div class="team-carousel" id="teamCarousel">
                <div class="team-member">
                  <div class="member-avatar">
                    <div class="avatar-placeholder">
                      <i class="fas fa-user"></i>
                    </div>
                  </div>
                  <div class="member-info">
                    <h3 class="member-name">于广军</h3>
                    <h4 class="member-position">银河星尘健康医疗专家</h4>
                    <p class="member-description">
                      博士，研究员，博士生导师。香港中文大学（深圳）教授、博士生导师香港中文大学（深圳）医学院副院长，国家健康医疗大数据研究院（深圳）院长，香港中文大学（深圳）医学院附属第二医院院长，上海交大中国医院发展研究院医疗信息研究所所长，上海儿童精准医学大数据工程技术研究中心主任，中国医院协会信息专委会副主委上海医学会互联网医疗分会主委。国家百千万人才工程计划，人社部有突出贡献专家。上海卫生系统优秀学科带头人、领军人才、优秀学术带头人、政府儿童工作白玉兰奖等。
                    </p>
                  </div>
                </div>
                <div class="team-member">
                  <div class="member-avatar">
                    <div class="avatar-placeholder">
                      <i class="fas fa-user"></i>
                    </div>
                  </div>
                  <div class="member-info">
                    <h3 class="member-name">刘起勇</h3>
                    <h4 class="member-position">银河星尘病媒首席科学家</h4>
                    <p class="member-description">
                      博士，研究员，博士生导师，中国疾病预防控制中心病媒控制首席专家，领导中国健康方面的国家研究。领导世界卫生组织（WHO）病媒监测与管理合作中心。世界卫生组织西太平洋地区办事处（WPRO）、世卫组织病媒控制顾问、世卫组织西太平洋区域办事处传染病监测和反应医务官员（STP）、世卫组织全球病媒控制常设委员会（GVCS）成员、创新病媒控制联盟（IVCC）理事，世界卫生组织病媒生物安全中心主任，4.pngPI，成立了国际登革热病媒管理联盟（IFSVM），可持续病媒管理国际论坛（IFSVM）执行主席，全国公共卫生与预防医学名词审定委员会媒介生物控制学分委会主任，中华预防医学会媒介生物学及控制分会主任委员，入选全球前10万名顶尖科学家，有超过35年公共卫生和研究经验。
                    </p>
                  </div>
                </div>
                <div class="team-member">
                  <div class="member-avatar">
                    <div class="avatar-placeholder">
                      <i class="fas fa-user"></i>
                    </div>
                  </div>
                  <div class="member-info">
                    <h3 class="member-name">李建东</h3>
                    <h4 class="member-position">银河星尘病毒首席专家</h4>
                    <p class="member-description">
                      1990-1995年于同济医科大学公共卫生学院预防医学专业获医学学士学位；1999-2002年，在中国预防医学科学院病毒学研究所获得免疫学专业硕士学位；2002-2006年，在德国布伦瑞克大学和德国感染研究中心学习，获自然科学博士学位。先后在中国预防医学科学院疾病控制处、中国疾病预防控制中心病毒病所工作，现为病毒病所病毒性出血热室副主任。多年来，主要从事出血热相关病毒病的预防控制工作，出血热相关病毒实验室诊断、分子流行病学调查、病毒宿主相互作用、传播机制以及新型免疫原制备等方面的研究。
                    </p>
                  </div>
                </div>
                <div class="team-member">
                  <div class="member-avatar">
                    <div class="avatar-placeholder">
                      <i class="fas fa-user"></i>
                    </div>
                  </div>
                  <div class="member-info">
                    <h3 class="member-name">陈石磊</h3>
                    <h4 class="member-position">银河星尘总经理</h4>
                    <p class="member-description">
                      斯坦福大学计算机科学博士、香港理工大学硕士。斯坦福SAIL成员，微联智能创始人，中青林集团AI科学家，工信部认证高级人工智能应用专家。曾任百度研究院研究员、AI架构师，湖南广电集团芒果TV大数据中心项目总监。研究方向包括人工智能、深度学习（DL）、强化学习及大规模机器学习系统（ML）、健康公益与AI应用场景，在相关领域发表7篇论文，同时发表有国家发明专利三项，实用新型专利两项。2014年和2020年分别参与过研发小度智能音响、百度智能云、文心一言等百度集团AI产品项目。2020年到2024年分别主导研发过中青林集团、百果园集团、南方电网、SKG未来健康、安克创新、中国烟草等头部企业的领域大模型项目落地及应用。
                    </p>
                  </div>
                </div>
                <div class="team-member">
                  <div class="member-avatar">
                    <div class="avatar-placeholder">
                      <i class="fas fa-user"></i>
                    </div>
                  </div>
                  <div class="member-info">
                    <h3 class="member-name">周绍鹏</h3>
                    <h4 class="member-position">市场总监</h4>
                    <p class="member-description">
                      湘潭大学经济学硕士，工信部认证高级大模型应用工程师。银河星尘市场总监，负责制定并执行公司市场战略，塑造品牌影响力，驱动业务增长和市场份额提升。环卫科技网市场解决方案专家，新疆博乐机关事业单位培训专家，济通集团运营体系设计特聘专家。曾就职于上市企业湘电集团军工制造特电事业部、深圳市洁亚环保产业有限公司环保事业部经理。前深圳市微米生物技术有限公司分类事业部总经理。15年行业市场经历，具备多年业务及管理双重思维实践经历。
                    </p>
                  </div>
                </div>
                <div class="team-member">
                  <div class="member-avatar">
                    <div class="avatar-placeholder">
                      <i class="fas fa-user"></i>
                    </div>
                  </div>
                  <div class="member-info">
                    <h3 class="member-name">杨利娅</h3>
                    <h4 class="member-position">产品总监</h4>
                    <p class="member-description">
                      软件工程硕士，工信部认证高级大模型应用工程师、高级提示词工程师。银河星尘
                      AI
                      产品中心总监，负责产品全生命周期管理与商业价值同步增长，确保公司产品组合在竞争中保持领先。曾任腾讯集团高级产品经理、平安集团
                      ToB 业务终端产品及运营负责人、集团 AI 战略项目 PMO
                      经理、数据分组经理，AI 及应用产品经理。在
                      ASR、OCR、NLP、Saas、AI agent、AI
                      大模型、大数据及数字化转型领域有丰富的从0-1搭建及运营经验，具备一线业务思维、数据化管理思维和互联网产品设计、运营管理思维。在大模型与行业应用结合方面有丰富落地经验。
                    </p>
                  </div>
                </div>
                <div class="team-member">
                  <div class="member-avatar">
                    <div class="avatar-placeholder">
                      <i class="fas fa-user"></i>
                    </div>
                  </div>
                  <div class="member-info">
                    <h3 class="member-name">陈文博</h3>
                    <h4 class="member-position">项目总监</h4>
                    <p class="member-description">
                      西北工业大学工学硕士，工信部认证高级大模型应用工程师。银河星尘
                      AI
                      项目总监，负责公司级项目群管理与交付，确保进度、成本、质量、风险全面可控，为业务目标达成提供保障。曾先后担任深圳易思博技术、铱云科技、科脉技术用户体验部负责人，成功带领团队打造了多款用户体验卓越的AI及应用类产品，负责CX体验战略设计、人机交互、NPS、体验度量体系（ROX），擅长将业务目标与AI能力、用户需求以体验场景进行深度融合，并以此为基础制定体验战略设计，推动全链路用户生命周期管理的落地实施及价值提升。
                    </p>
                  </div>
                </div>
                <div class="team-member">
                  <div class="member-avatar">
                    <div class="avatar-placeholder">
                      <i class="fas fa-user"></i>
                    </div>
                  </div>
                  <div class="member-info">
                    <h3 class="member-name">黄裕玮</h3>
                    <h4 class="member-position">技术总监</h4>
                    <p class="member-description">
                      软件工程硕士，工信部认证AIGC应用工程师。银河星尘AI平台总监，负责公司所有平台的统筹开发，技术架构和各个领域的技术实现方案落地。以业务为核心进行定制化，PC，移动端，手持等多场景化的功能支撑。曾任多家上市公司核心团队负责人，主导以技术创新驱动业务增长，千人级研发团队项目管理体系建设，推动SOP流程标准化与研发效能平台落地。统筹百人综合技术领域团队，在人才梯队搭建、激励机制与组织效能提升方面成果显著。精通多领域技术架构规划与实施，以技术战略赋能业务规模化扩张。
                    </p>
                  </div>
                </div>
                <div class="team-member">
                  <div class="member-avatar">
                    <div class="avatar-placeholder">
                      <i class="fas fa-user"></i>
                    </div>
                  </div>
                  <div class="member-info">
                    <h3 class="member-name">李锦兴</h3>
                    <h4 class="member-position">银河星尘AI科学家</h4>
                    <p class="member-description">
                      香港理工大学博士，哈尔滨工业大学（深圳）计算机科学与技术学院青年拔尖副教授、博士生导师。研究方向主要包括计算机视觉、模式识别等，在TIP、TNNLS、Inf.
                      Fus.、CVPR、NeurIPS、ACM
                      MM、AAAI等国际著名期刊和会议发表50余篇学术论文，在中医四诊融合方面有着多年的研究经验，主持了“多模态体外诊察信息智能感知及连续健康计算研究”、“复杂环境下的多模态无创体表信息融合及诊断研究”、“基于三部九侯的多通道脉象融合诊断方法研究”等国家级、市级科研项目，开发了中医四诊融合系统等。
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 第四屏：社会责任 -->
      <section class="responsibility-screen page-section">
        <div class="responsibility-background">
          <div class="responsibility-overlay"></div>
          <div class="container">
            <div class="responsibility-content">
              <h2 class="responsibility-title">我们的责任</h2>
              <div class="responsibility-text">
                <p class="responsibility-paragraph">
                  银河星尘致力于借助尖端人工智能技术，为中国乃至全球各地提供便捷、创新且高效的解决方案，以有效应对媒介疾病传播的挑战。
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- 页脚 -->
    <div id="footer-container"></div>
    <!--footer iframe end-->

    <script src="js/common.js"></script>
    <style>
      /* 团队区域整体居中包装器 */
      .team-section-wrapper {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 100vh;
        padding: 60px 20px;
      }

      .team-header {
        text-align: center;
        margin-bottom: 60px;
      }

      /* 团队轮播导航按钮样式 */
      .team-carousel-container {
        position: relative;
        overflow: hidden;
        width: 100%;
        max-width: 1440px;
        display: flex;
        justify-content: flex-start;
      }

      .team-nav-btn {
        position: absolute;
        display: none !important;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(255, 255, 255, 0.9);
        border: none;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        z-index: 10;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
      }

      .team-nav-btn:hover {
        background: rgba(255, 255, 255, 1);
        transform: translateY(-50%) scale(1.1);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
      }

      .team-prev-btn {
        left: 20px;
      }

      .team-next-btn {
        right: 20px;
      }

      .team-nav-btn i {
        font-size: 18px;
        color: #333;
      }

      .team-carousel {
        display: flex;
        transition: transform 0.5s ease;
        justify-content: flex-start;
        align-items: stretch;
        flex-wrap: nowrap;
      }

      .team-member {
        flex: 0 0 auto;
        width: auto; /* 自动宽度，根据内容调整 */
        min-width: 300px; /* 最小宽度 */
        max-width: 450px; /* 最大宽度 */
        height: 600px; /* 固定高度确保等高 */
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        border-radius: 16px;
        padding: 25px;
        margin: 0 15px;
        border: 1px solid rgba(255, 255, 255, 0.3);
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }

      .team-member:hover {
        transform: translateY(-5px);
        background: rgba(255, 255, 255, 0.95);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
      }

      .member-avatar {
        width: 80px;
        height: 80px;
        margin: 0 auto 20px auto;
        border-radius: 50%;
        overflow: hidden;
        background: linear-gradient(135deg, #0066ff, #00d4ff);
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .avatar-placeholder {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 2rem;
      }

      .member-info {
        text-align: center;
        flex: 1;
        display: flex;
        flex-direction: column;
      }

      .member-name {
        font-size: 1.5rem;
        font-weight: 700;
        color: #1a1a1a;
        margin-bottom: 8px;
      }

      .member-position {
        font-size: 1.1rem;
        color: #0066ff;
        margin-bottom: 15px;
        font-weight: 500;
      }

      .member-description {
        font-size: 0.85rem;
        color: #333333;
        line-height: 1.5;
        text-align: left;
        flex: 1;
        overflow-y: auto;
        margin-top: auto;
        word-wrap: break-word;
        hyphens: auto;
      }

      /* 响应式设计 */
      @media (max-width: 768px) {
        .team-member {
          min-width: 300px;
          max-width: 400px;
          height: 550px;
          padding: 20px;
        }

        .team-nav-btn {
          width: 40px;
          height: 40px;
        }

        .team-prev-btn {
          left: 10px;
        }

        .team-next-btn {
          right: 10px;
        }

        .member-name {
          font-size: 1.3rem;
        }

        .member-position {
          font-size: 1rem;
        }

        .member-description {
          font-size: 0.85rem;
        }
      }

      @media (max-width: 480px) {
        .team-member {
          min-width: 280px;
          max-width: 350px;
          height: 500px;
          padding: 15px;
        }

        .member-name {
          font-size: 1.2rem;
        }

        .member-position {
          font-size: 0.95rem;
        }

        .member-description {
          font-size: 0.8rem;
        }
      }
    </style>
    <script>
      // 页面整体滑动动画初始化
      document.addEventListener("DOMContentLoaded", function () {
        // 创建Intersection Observer来监控元素进入视口
        const observer = new IntersectionObserver(
          (entries) => {
            entries.forEach((entry) => {
              if (entry.isIntersecting) {
                // 添加动画类
                entry.target.classList.add("animate-in");
              } else {
                // 元素离开视口时移除动画类（支持反向动画）
                entry.target.classList.remove("animate-in");
              }
            });
          },
          {
            threshold: 0.1, // 当元素10%可见时触发
            rootMargin: "0px 0px -50px 0px", // 提前50px触发动画
          }
        );

        // 为所有需要动画的元素添加观察
        const animatedElements = document.querySelectorAll(
          ".page-section, .slide-in-left, .slide-in-right, .slide-in-scale"
        );
        animatedElements.forEach((el) => {
          observer.observe(el);
        });

        // 团队轮播功能
        setTimeout(() => {
          initTeamCarousel();
        }, 1000);
      });

      function initTeamCarousel() {
        const carousel = document.getElementById("teamCarousel");
        const prevBtn = document.getElementById("teamPrevBtn");
        const nextBtn = document.getElementById("teamNextBtn");
        const members = carousel.querySelectorAll(".team-member");

        if (!carousel || !prevBtn || !nextBtn || members.length === 0) return;

        let currentIndex = 0;

        const memberWidth = members[0].offsetWidth + 30; // 450px width + 30px gap
        const visibleMembers = window.innerWidth > 768 ? 3 : 1; // 桌面显示3个，移动端显示1个
        const maxIndex = Math.max(0, members.length - visibleMembers);

        function updateCarousel() {
          const translateX = -currentIndex * memberWidth;
          carousel.style.transform = `translateX(${translateX}px)`;

          // 更新按钮状态
          prevBtn.style.opacity = currentIndex === 0 ? "0.5" : "1";
          nextBtn.style.opacity = currentIndex >= maxIndex ? "0.5" : "1";
          prevBtn.disabled = currentIndex === 0;
          nextBtn.disabled = currentIndex >= maxIndex;
        }

        prevBtn.addEventListener("click", () => {
          if (currentIndex > 0) {
            currentIndex--;
            updateCarousel();
          }
        });

        nextBtn.addEventListener("click", () => {
          if (currentIndex < maxIndex) {
            currentIndex++;
            updateCarousel();
          }
        });

        // 触摸滑动支持
        let startX = 0;
        let isDragging = false;

        carousel.addEventListener("touchstart", (e) => {
          startX = e.touches[0].clientX;
          isDragging = true;
        });

        carousel.addEventListener("touchmove", (e) => {
          if (!isDragging) return;
          e.preventDefault();
        });

        carousel.addEventListener("touchend", (e) => {
          if (!isDragging) return;
          isDragging = false;

          const endX = e.changedTouches[0].clientX;
          const diffX = startX - endX;

          if (Math.abs(diffX) > 50) {
            // 滑动距离超过50px才触发
            if (diffX > 0 && currentIndex < maxIndex) {
              // 向左滑动，显示下一个
              currentIndex++;
              updateCarousel();
            } else if (diffX < 0 && currentIndex > 0) {
              // 向右滑动，显示上一个
              currentIndex--;
              updateCarousel();
            }
          }
        });

        // 响应式更新
        window.addEventListener("resize", () => {
          const newVisibleMembers = window.innerWidth > 768 ? 3 : 1;
          const newMaxIndex = Math.max(0, members.length - newVisibleMembers);
          if (currentIndex > newMaxIndex) {
            currentIndex = newMaxIndex;
          }
          updateCarousel();
        });

        // 自动轮播功能
        let autoPlayInterval;

        function startAutoPlay() {
          autoPlayInterval = setInterval(() => {
            if (currentIndex < maxIndex) {
              currentIndex++;
            } else {
              currentIndex = 0;
            }
            updateCarousel();
          }, 2000); // 2秒轮播一次
        }

        function stopAutoPlay() {
          if (autoPlayInterval) {
            clearInterval(autoPlayInterval);
          }
        }

        // 鼠标悬停时停止自动轮播
        carousel.addEventListener("mouseenter", stopAutoPlay);
        carousel.addEventListener("mouseleave", startAutoPlay);

        // 初始化
        updateCarousel();
        startAutoPlay();
      }
    </script>
    <script src="js/footer.js"></script>
  </body>
</html>

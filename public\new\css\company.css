/* company.html 专用样式 */

/* 公司页面主容器 */
.company-main {
  padding-top: 80px;
}

/* 公司页面头部 */
.company-hero {
  padding: 80px 0 60px;
  background: #ffffff;
  position: relative;
  overflow: hidden;
}

.company-hero .container {
  max-width: 95%;
  margin: 0 auto;
  padding: 0 max(3%, 30px);
}

.company-header {
  position: relative;
  z-index: 2;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 80px;
  min-height: 400px;
}

.header-left {
  flex: 0 0 auto;
  min-width: 350px;
}

.company-title {
  font-size: 8rem;
  font-weight: 900;
  color: #f0f0f0;
  text-transform: uppercase;
  letter-spacing: -0.02em;
  line-height: 0.9;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, #fce7f3 0%, #ec4899 50%, #be185d 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.header-right {
  flex: 1;
  margin-left: 100px;
}

.company-info {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(226, 232, 240, 0.3);
  border-radius: 24px;
  padding: 40px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
}

.company-info-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 20px;
  line-height: 1.2;
}

.company-description {
  font-size: 1.2rem;
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0;
}

.highlight-text {
  color: #be185d;
  font-weight: 600;
}

/* 浮动的毛玻璃球 - 粉色主题 */
.glass-ball-pink-1 {
  position: absolute;
  top: 15%;
  left: 8%;
  width: 250px;
  height: 250px;
  background: radial-gradient(circle, rgba(236, 72, 153, 0.12) 0%, rgba(190, 24, 93, 0.08) 50%, transparent 70%);
  backdrop-filter: blur(20px);
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.3);
  animation: floatPinkGlassBall1 15s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
}

.glass-ball-pink-2 {
  position: absolute;
  top: 45%;
  right: 12%;
  width: 180px;
  height: 180px;
  background: radial-gradient(circle, rgba(190, 24, 93, 0.1) 0%, rgba(157, 23, 77, 0.06) 50%, transparent 70%);
  backdrop-filter: blur(15px);
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.25);
  animation: floatPinkGlassBall2 18s ease-in-out infinite reverse;
  pointer-events: none;
  z-index: 1;
}

.glass-ball-pink-3 {
  position: absolute;
  bottom: 20%;
  left: 15%;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(252, 231, 243, 0.08) 0%, rgba(236, 72, 153, 0.05) 50%, transparent 70%);
  backdrop-filter: blur(18px);
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: floatPinkGlassBall3 20s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
}

/* 粉色毛玻璃球动画 */
@keyframes floatPinkGlassBall1 {
  0%, 100% { transform: translate(0, 0) rotate(0deg); }
  25% { transform: translate(30px, -20px) rotate(90deg); }
  50% { transform: translate(-20px, 30px) rotate(180deg); }
  75% { transform: translate(40px, 10px) rotate(270deg); }
}

@keyframes floatPinkGlassBall2 {
  0%, 100% { transform: translate(0, 0) scale(1); }
  33% { transform: translate(-25px, 20px) scale(1.1); }
  66% { transform: translate(35px, -15px) scale(0.9); }
}

@keyframes floatPinkGlassBall3 {
  0%, 100% { transform: translate(0, 0) rotate(0deg) scale(1); }
  50% { transform: translate(-30px, -25px) rotate(180deg) scale(1.05); }
}

/* 公司介绍区域 */
.company-intro {
  padding: 80px 0;
  background: #f8fafb;
}

.intro-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.intro-text {
  padding: 40px;
}

.intro-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 30px;
  line-height: 1.2;
}

.intro-description {
  font-size: 1.1rem;
  color: var(--text-secondary);
  line-height: 1.7;
  margin-bottom: 30px;
}

.intro-features {
  list-style: none;
  padding: 0;
  margin: 0;
}

.intro-features li {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
  font-size: 1rem;
  color: var(--text-secondary);
}

.intro-features li::before {
  content: '✓';
  color: #be185d;
  font-weight: bold;
  font-size: 1.2rem;
}

.intro-image {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.12);
}

.intro-img {
  width: 100%;
  height: 400px;
  object-fit: cover;
}

/* 团队展示区域 */
.team-section {
  padding: 80px 0;
  background: white;
}

.team-header {
  text-align: center;
  margin-bottom: 60px;
}

.team-title {
  font-size: 3rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 20px;
}

.team-subtitle {
  font-size: 1.2rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 40px;
}

.team-member {
  background: white;
  border-radius: 20px;
  padding: 30px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(226, 232, 240, 0.3);
  transition: all 0.3s ease;
}

.team-member:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.12);
}

.member-avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  margin: 0 auto 20px;
  background: linear-gradient(135deg, #fce7f3 0%, #ec4899 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  color: white;
  font-weight: bold;
}

.member-name {
  font-size: 1.3rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.member-position {
  font-size: 1rem;
  color: #be185d;
  font-weight: 500;
  margin-bottom: 15px;
}

.member-description {
  font-size: 0.95rem;
  color: var(--text-secondary);
  line-height: 1.5;
}

/* 公司价值观区域 */
.values-section {
  padding: 80px 0;
  background: linear-gradient(135deg, #fce7f3 0%, #ec4899 100%);
  color: white;
}

.values-header {
  text-align: center;
  margin-bottom: 60px;
}

.values-title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 20px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
}

.values-subtitle {
  font-size: 1.2rem;
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto;
}

.values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
}

.value-item {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 40px 30px;
  text-align: center;
  transition: all 0.3s ease;
}

.value-item:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.15);
}

.value-icon {
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  font-size: 2rem;
}

.value-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 15px;
}

.value-description {
  font-size: 1rem;
  line-height: 1.6;
  opacity: 0.9;
}

/* 响应式设计 - 移动端适配 */
@media (max-width: 768px) {
  .company-hero {
    padding: 60px 0 40px;
  }

  .header-content {
    flex-direction: column;
    gap: 40px;
    min-height: auto;
    text-align: center;
  }

  .header-left {
    min-width: auto;
    width: 100%;
  }

  .company-title {
    font-size: 4rem;
  }

  .header-right {
    margin-left: 0;
    width: 100%;
  }

  .company-info {
    padding: 30px 20px;
  }

  .company-info-title {
    font-size: 2rem;
  }

  .company-description {
    font-size: 1rem;
  }

  .intro-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .intro-text {
    padding: 20px;
  }

  .intro-title {
    font-size: 2rem;
  }

  .intro-description {
    font-size: 1rem;
  }

  .intro-img {
    height: 300px;
  }

  .team-title {
    font-size: 2.2rem;
  }

  .team-subtitle {
    font-size: 1rem;
  }

  .team-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .team-member {
    padding: 25px;
  }

  .member-avatar {
    width: 100px;
    height: 100px;
    font-size: 2.5rem;
  }

  .values-title {
    font-size: 2.2rem;
  }

  .values-subtitle {
    font-size: 1rem;
  }

  .values-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .value-item {
    padding: 30px 20px;
  }

  .value-icon {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }

  .value-title {
    font-size: 1.3rem;
  }

  /* 隐藏部分毛玻璃球以减少移动端视觉干扰 */
  .glass-ball-pink-3 {
    display: none;
  }

  .glass-ball-pink-1,
  .glass-ball-pink-2 {
    transform: scale(0.6);
  }
}

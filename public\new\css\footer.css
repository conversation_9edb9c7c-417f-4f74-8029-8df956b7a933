/* footer.html 专用样式 */

/* 页脚主容器 */
.footer {
  background: linear-gradient(135deg, #0a0f1a 0%, #1e293b 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
}

/* 页脚主要内容 */
.footer-main {
  padding: 80px 0 40px;
  position: relative;
  z-index: 2;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 60px;
  margin-bottom: 60px;
}

/* 公司信息区域 */
.footer-company {
  max-width: 400px;
}

.footer-logo {
  display: flex;
  align-items: center;
  margin-bottom: 25px;
}

.footer-logo-img {
  width: 60px;
  height: 60px;
  object-fit: contain;
  border-radius: 8px;
  margin-right: 15px;
}

.footer-logo-text {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
}

.footer-description {
  font-size: 1rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 30px;
}

.footer-contact {
  margin-bottom: 25px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
  font-size: 0.95rem;
  color: rgba(255, 255, 255, 0.8);
}

.contact-item i {
  color: var(--accent-cyan);
  width: 16px;
  text-align: center;
}

.footer-social {
  display: flex;
  gap: 15px;
}

.social-link {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-decoration: none;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.social-link:hover {
  background: var(--accent-cyan);
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 212, 255, 0.3);
}

/* 页脚链接区域 */
.footer-section {
  min-width: 150px;
}

.footer-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: white;
  margin-bottom: 25px;
  position: relative;
}

.footer-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 30px;
  height: 2px;
  background: var(--accent-cyan);
  border-radius: 1px;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 12px;
}

.footer-links a {
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  display: inline-block;
}

.footer-links a:hover {
  color: var(--accent-cyan);
  transform: translateX(5px);
}

/* 页脚底部 */
.footer-bottom {
  padding: 30px 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  z-index: 2;
}

.footer-bottom-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.footer-copyright {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.6);
}

.footer-legal {
  display: flex;
  gap: 30px;
}

.footer-legal a {
  color: rgba(255, 255, 255, 0.6);
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

.footer-legal a:hover {
  color: var(--accent-cyan);
}

/* 返回顶部按钮 */
.back-to-top {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  background: var(--gradient-primary);
  border: none;
  border-radius: 50%;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px);
  box-shadow: 0 4px 20px rgba(0, 102, 255, 0.3);
}

.back-to-top.visible {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.back-to-top:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 102, 255, 0.4);
}

/* 页脚装饰元素 */
.footer-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.footer-orb {
  position: absolute;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(0, 212, 255, 0.1) 0%, rgba(0, 102, 255, 0.05) 50%, transparent 70%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  animation: floatFooterOrb 25s ease-in-out infinite;
}

.footer-orb-1 {
  width: 200px;
  height: 200px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.footer-orb-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 15%;
  animation-delay: 8s;
}

.footer-orb-3 {
  width: 120px;
  height: 120px;
  bottom: 30%;
  left: 70%;
  animation-delay: 16s;
}

@keyframes floatFooterOrb {
  0%, 100% {
    transform: translate(0, 0) scale(1);
    opacity: 0.3;
  }
  25% {
    transform: translate(20px, -30px) scale(1.1);
    opacity: 0.5;
  }
  50% {
    transform: translate(-15px, 20px) scale(0.9);
    opacity: 0.4;
  }
  75% {
    transform: translate(25px, 15px) scale(1.05);
    opacity: 0.6;
  }
}

/* 响应式设计 - 移动端适配 */
@media (max-width: 768px) {
  .footer-main {
    padding: 60px 0 30px;
  }

  .footer-container {
    padding: 0 1rem;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 40px;
    margin-bottom: 40px;
  }

  .footer-company {
    max-width: none;
    text-align: center;
  }

  .footer-logo {
    justify-content: center;
  }

  .footer-social {
    justify-content: center;
  }

  .footer-section {
    text-align: center;
  }

  .footer-title::after {
    left: 50%;
    transform: translateX(-50%);
  }

  .footer-bottom-content {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }

  .footer-legal {
    gap: 20px;
  }

  .back-to-top {
    bottom: 20px;
    right: 20px;
    width: 45px;
    height: 45px;
    font-size: 1rem;
  }

  /* 隐藏部分装饰元素以减少移动端视觉干扰 */
  .footer-orb-2,
  .footer-orb-3 {
    display: none;
  }

  .footer-orb-1 {
    transform: scale(0.7);
  }
}

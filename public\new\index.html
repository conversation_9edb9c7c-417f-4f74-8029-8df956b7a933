<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>银河星尘 - 病媒AI大模型监测系统</title>
    <link rel="stylesheet" href="css/common.css" />
    <link rel="stylesheet" href="css/index.css" />
    <link href="css/all.min.css" rel="stylesheet" />
    <script src="js/gsap.min.js"></script>
    <script src="js/ScrollTrigger.min.js"></script>

      .hero-third-stats .stat-number-static {
        font-size: 4rem;
        font-weight: bold;
        color: white;
        line-height: 1;
      }

      .hero-third-stats .stat-unit {
        font-size: 1.5rem;
        color: white;
        opacity: 0.8;
      }

      .hero-third-stats .stat-label {
        margin-top: 8px;
        font-size: 1rem;
        color: white !important;
        opacity: 0.9;
      }

      /* 响应式设计 */
      @media (max-width: 768px) {
        .hero-third-layout {
          bottom: 40px;
          left: 30px;
        }

        .hero-third-stats .stat-group {
          gap: 40px;
        }

        .hero-third-stats .stat-number-static {
          font-size: 3rem;
        }

        .hero-third-stats .stat-unit {
          font-size: 1.2rem;
        }
      }

      @media (max-width: 320px) {
        .hero-third-layout {
          bottom: 20px;
          left: 20px;
        }

        .hero-third-stats .stat-group {
          flex-direction: column !important;
          gap: 20px;
        }

        .hero-third-stats .stat-item-third {
          min-width: auto;
        }

        .hero-third-stats .stat-number-static {
          font-size: 2.5rem;
        }
      }

      /* 强制横向排列 - 适用于大部分设备 */
      @media (min-width: 321px) {
        .hero-third-stats .stat-group {
          flex-direction: row !important;
        }
      }
    </style>
  </head>
  <body>
    <!-- 导航栏 -->
    <nav class="navbar">
      <div class="nav-container">
        <div class="nav-logo">
          <img src="company_logo.png" alt="银河星尘" class="logo-image" />
        </div>
        <ul class="nav-menu">
          <li class="nav-item">
            <a href="index.html" class="nav-link active">首页</a>
          </li>
          <li class="nav-item">
            <a href="products.html" class="nav-link">产品中心</a>
          </li>
          <li class="nav-item dropdown">
            <a href="scenarios.html" class="nav-link">应用场景</a>
            <div class="dropdown-menu">
              <a href="scenarios.html" class="dropdown-item">工地监测</a>
              <a href="scenarios.html" class="dropdown-item">公园管理</a>
              <a href="scenarios.html" class="dropdown-item">垃圾场治理</a>
              <a href="scenarios.html" class="dropdown-item">学校防护</a>
              <a href="scenarios.html" class="dropdown-item">医院环境</a>
              <a href="scenarios.html" class="dropdown-item">园区服务</a>
              <a href="scenarios.html" class="dropdown-item">贸易市场</a>
              <a href="scenarios.html#cases" class="dropdown-item">落地案例</a>
            </div>
          </li>
          <li class="nav-item">
            <a href="news.html" class="nav-link">新闻中心</a>
          </li>
          <li class="nav-item">
            <a href="company.html" class="nav-link">企业信息</a>
          </li>
        </ul>
        <div class="nav-toggle">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>
    </nav>

    <main>
      <!-- 第一屏：视频背景 -->
      <section id="home" class="hero-video-section">
        <!-- 视频背景 -->
        <video class="hero-video" autoplay muted loop playsinline>
          <source src="banner.mp4" type="video/mp4" />
          您的浏览器不支持视频标签。
        </video>

        <!-- 内容区域 -->
        <div class="hero-content">
          <!-- 初始内容 -->
          <div class="hero-initial-content active">
            <div class="hero-text">
              <h1 class="hero-title">以创新为刃 护病媒可控</h1>
              <p class="hero-subtitle">
                以人工智能为引擎，赋能公共卫生防疫体系，守护人类健康安全
              </p>
              <div class="hero-logo">
                <img
                  src="company_name_en.svg"
                  alt="Galaxy Stardust Technology"
                  class="company-logo"
                />
              </div>
            </div>
          </div>

          <!-- 滚动后内容 - 第二模块 -->
          <div class="hero-scroll-content">
            <div class="hero-split-layout">
              <div class="hero-left">
                <div class="hero-left-text">
                  <div class="hero-left-line">IS</div>
                  <div class="hero-left-line">AI-Powered Vector Control</div>
                  <div class="hero-left-line">Pioneer</div>
                </div>
              </div>
              <div class="hero-right">
                <h2 class="hero-right-title">行业领航者</h2>
                <div class="hero-right-description">
                  <p>
                    银河星尘是一家领先的人工智能技术高科技公司，汇聚来自人工智能、医疗健康及病媒生物三大领域的顶尖专家科研团队，与业内权威机构建立了稳固的战略合作关系。
                  </p>
                  <p>
                    公司采用最先进的技术手段，融合病媒AI大模型及智能物联网(IoT)设备，深度学习及数据分析技术，构建了一套全面的病媒监测预警防控系统。该系统能够实时监测病媒生物的分布情况、活动规律和密度变化，为用户提供精准的预警防控服务。
                  </p>
                </div>
              </div>
            </div>
          </div>

          <!-- 第三模块内容 -->
          <div class="hero-third-content">
            <div class="hero-third-layout">
              <div class="hero-third-header">
                <h2 class="hero-third-title">Vector Monitoring</h2>
                <p class="hero-third-subtitle">病媒监测预警防控领域首选</p>
              </div>
              <div class="hero-third-stats">
                <div class="stat-group">
                  <div class="stat-item-third">
                    <div class="stat-number-container">
                      <span class="stat-number-static">100</span>
                      <span class="stat-unit">%</span>
                    </div>
                    <div class="stat-label">监测覆盖率</div>
                  </div>
                  <div class="stat-item-third">
                    <div class="stat-number-container">
                      <span class="stat-number-static">95</span>
                      <span class="stat-unit">%</span>
                    </div>
                    <div class="stat-label">识别准确率</div>
                  </div>
                  <div class="stat-item-third">
                    <div class="stat-number-container">
                      <span class="stat-number-static">99.6</span>
                      <span class="stat-unit">%</span>
                    </div>
                    <div class="stat-label">预警准确率</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 滚动进度指示器 -->
          <div class="scroll-progress-indicator">
            <div class="progress-bar">
              <div class="progress-fill"></div>
            </div>
          </div>
        </div>

        <!-- 滚动指示 -->
        <div class="scroll-indicator">
          <span>向下滚动</span>
          <div class="scroll-arrow">
            <i class="fas fa-chevron-down"></i>
          </div>
        </div>
      </section>

      <!-- 第一屏占位空间 -->
      <div class="hero-spacer"></div>

      <script>
        document.addEventListener("DOMContentLoaded", function () {
          const heroVideoSection = document.querySelector(
            ".hero-video-section"
          );
          const initialContent = document.querySelector(
            ".hero-initial-content"
          );
          const scrollContent = document.querySelector(".hero-scroll-content");
          const thirdContent = document.querySelector(".hero-third-content");
          const progressFill = document.querySelector(".progress-fill");

          let currentStage = 0; // 0: 初始, 1: 第二模块, 2: 第三模块
          let isAutoPlaying = true; // 是否在自动播放阶段
          let hasCompletedAutoPlay = false; // 是否已完成自动播放
          let isInFirstScreen = true; // 是否在第一屏

          const firstThreshold = 250;
          const secondThreshold = 600;
          const completeThreshold = 1000;

          // 切换到指定阶段
          function switchToStage(stage) {
            if (stage === 1 && currentStage !== 1) {
              // 切换到第二模块
              initialContent.classList.add("fade-out");
              scrollContent.classList.add("fade-in");
              scrollContent.classList.remove("slide-out-up", "slide-out-down");
              thirdContent.classList.add("slide-out-down");
              thirdContent.classList.remove("fade-in");
              currentStage = 1;
            } else if (stage === 2 && currentStage !== 2) {
              // 切换到第三模块
              initialContent.classList.add("fade-out");
              scrollContent.classList.add("slide-out-up");
              scrollContent.classList.remove("fade-in");
              thirdContent.classList.add("fade-in");
              thirdContent.classList.remove("slide-out-down");
              currentStage = 2;
            } else if (stage === 0 && currentStage !== 0) {
              // 切换回初始内容
              initialContent.classList.remove("fade-out");
              scrollContent.classList.add("slide-out-down");
              scrollContent.classList.remove("fade-in");
              thirdContent.classList.add("slide-out-down");
              thirdContent.classList.remove("fade-in");
              currentStage = 0;
            }
          }

          // 自动播放三个阶段（仅初次进入时）
          function startAutoPlay() {
            // 第一阶段：显示3秒
            setTimeout(() => {
              if (isAutoPlaying) {
                switchToStage(1); // 切换到第二阶段
              }
            }, 3000);

            // 第二阶段：显示3秒
            setTimeout(() => {
              if (isAutoPlaying) {
                switchToStage(2); // 切换到第三阶段
              }
            }, 6000);

            // 第三阶段：显示3秒后自动滚动到第二屏
            setTimeout(() => {
              if (isAutoPlaying) {
                hasCompletedAutoPlay = true;
                isAutoPlaying = false;
                isInFirstScreen = false;

                // 自动滚动到解决方案部分
                const solutionsSection =
                  document.querySelector(".solutions-section");
                if (solutionsSection) {
                  solutionsSection.scrollIntoView({ behavior: "smooth" });
                }
              }
            }, 9000);
          }

          // 处理滚动事件
          function handleScroll() {
            const scrollY = window.scrollY;
            const heroHeight = window.innerHeight;

            // 如果还在自动播放阶段，阻止滚动
            if (isAutoPlaying) {
              return;
            }

            // 判断是否在第一屏
            if (scrollY < heroHeight) {
              isInFirstScreen = true;

              // 根据滚动位置切换阶段（手动滚动时）
              if (scrollY >= secondThreshold && currentStage !== 2) {
                switchToStage(2);
              } else if (
                scrollY >= firstThreshold &&
                scrollY < secondThreshold &&
                currentStage !== 1
              ) {
                switchToStage(1);
              } else if (scrollY < firstThreshold && currentStage !== 0) {
                switchToStage(0);
              }

              // 视频固定在第一屏
              heroVideoSection.style.position = "fixed";
              heroVideoSection.style.top = "0";
              heroVideoSection.style.opacity = "1";
            } else {
              isInFirstScreen = false;

              // 离开第一屏后的视频处理
              heroVideoSection.style.position = "absolute";
              heroVideoSection.style.top = "0";

              // 计算淡出进度
              const fadeDistance = heroHeight * 0.3;
              const fadeProgress = Math.min(
                (scrollY - heroHeight) / fadeDistance,
                1
              );
              heroVideoSection.style.opacity = 1 - fadeProgress;
            }

            // 更新进度条
            if (progressFill && isInFirstScreen) {
              const scrollProgress = Math.min(
                (scrollY / completeThreshold) * 100,
                100
              );
              progressFill.style.width = scrollProgress + "%";
            }
          }

          // 监听滚动事件
          window.addEventListener("scroll", handleScroll);

          // 防止用户在自动播放期间滚动
          window.addEventListener(
            "wheel",
            function (e) {
              if (isAutoPlaying) {
                e.preventDefault();
              }
            },
            { passive: false }
          );

          window.addEventListener(
            "touchmove",
            function (e) {
              if (isAutoPlaying) {
                e.preventDefault();
              }
            },
            { passive: false }
          );

          // 启动自动播放（仅初次进入时）
          startAutoPlay();

          // 初始化
          handleScroll();
        });
      </script>

      <!-- 高质效的数字监测解决方案 -->
      <section id="solutions" class="solutions-section">
        <!-- 动态背景效果 -->
        <div class="dynamic-background">
          <div class="glass-orb orb-1"></div>
          <div class="glass-orb orb-2"></div>
          <div class="glass-orb orb-3"></div>
          <div class="glass-orb orb-4"></div>
        </div>

        <div class="solutions-container">
          <!-- 标题区域 -->
          <div class="solutions-header">
            <!-- 主标题 -->
            <h2 class="solutions-main-title">高质效的数字监测解决方案</h2>

            <!-- 副标题 -->
            <p class="solutions-subtitle">
              通过深度融合AI大模型、智能物联网硬件和大数据分析技术，为客户提供更全面的病媒监测、分析一体化服务
            </p>
          </div>

          <!-- 架构图片 -->
          <div class="architecture-image-container">
            <img
              src="assets/first_page/framework.svg"
              alt="数字监测解决方案架构图"
              class="architecture-img"
            />
          </div>
        </div>
      </section>

      <!-- 第三屏：发展历程 -->
      <section id="timeline" class="timeline-section">
        <!-- 动态背景效果 -->
        <div class="dynamic-background">
          <div class="glass-orb orb-1"></div>
          <div class="glass-orb orb-2"></div>
          <div class="glass-orb orb-3"></div>
          <div class="glass-orb orb-4"></div>
        </div>

        <div class="timeline-container">
          <!-- 标题区域 -->
          <div class="timeline-header">
            <h2 class="solutions-main-title">银河星尘的发展历程</h2>
          </div>

          <!-- 时间线内容 -->
          <div class="timeline-content">
            <div class="timeline-track">
              <div class="timeline-items-container" id="timeline-items-container">
              </div>
            </div>
          </div>

          <!-- 合作机构轮播 -->
          <div class="partners-section">
            <div class="partners-carousel">
              <div class="partners-track">
                <div class="partner-item">
                  <img
                    src="hz_logo/hzjg/2.png"
                    alt="中国疾病预防控制中心（传染病预防控制所）"
                  />
                </div>
                <div class="partner-item">
                  <img
                    src="hz_logo/hzjg/3.png"
                    alt="中国疾病预防控制中心（慢性非传染性疾病预防控制中心）"
                  />
                </div>
                <div class="partner-item">
                  <img
                    src="hz_logo/hzjg/1.png"
                    alt="世界卫生组织媒介生物监测与管理合作中心"
                  />
                </div>
                <div class="partner-item">
                  <img src="hz_logo/hzjg/4.png"
                  alt="传染病溯源预警与智能决策全国重点实验室"">
                </div>
                <div class="partner-item">
                  <img
                    src="hz_logo/hzjg/6.png"
                    alt="国家健康医疗大数据研究院（深圳）"
                  />
                </div>
                <div class="partner-item">
                  <img src="hz_logo/hzjg/8.png" alt="香港中文大学（深圳）" />
                </div>
                <div class="partner-item">
                  <img src="hz_logo/hzjg/5.png" alt="哈尔滨工业大学（深圳）" />
                </div>
                <div class="partner-item">
                  <img src="hz_logo/hzjg/7.png" alt="深圳市大数据研究院" />
                </div>
                <!-- 重复一遍用于无缝轮播 -->
                <div class="partner-item">
                  <img
                    src="hz_logo/hzjg/2.png"
                    alt="中国疾病预防控制中心（传染病预防控制所）"
                  />
                </div>
                <div class="partner-item">
                  <img
                    src="hz_logo/hzjg/3.png"
                    alt="中国疾病预防控制中心（慢性非传染性疾病预防控制中心）"
                  />
                </div>
                <div class="partner-item">
                  <img
                    src="hz_logo/hzjg/1.png"
                    alt="世界卫生组织媒介生物监测与管理合作中心"
                  />
                </div>
                <div class="partner-item">
                  <img
                    src="hz_logo/hzjg/4.png"
                    alt="传染病溯源预警与智能决策全国重点实验室"
                  />
                </div>
                <div class="partner-item">
                  <img
                    src="hz_logo/hzjg/6.png"
                    alt="国家健康医疗大数据研究院（深圳）"
                  />
                </div>
                <div class="partner-item">
                  <img src="hz_logo/hzjg/8.png" alt="香港中文大学（深圳）" />
                </div>
                <div class="partner-item">
                  <img src="hz_logo/hzjg/5.png" alt="哈尔滨工业大学（深圳）" />
                </div>
                <div class="partner-item">
                  <img src="hz_logo/hzjg/7.png" alt="深圳市大数据研究院" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 第四屏：联系方式 -->
      <section id="contact" class="contact-section">
        <div class="contact-background">
          <div class="contact-content">
            <!-- 左上方标题文字 -->
            <div class="contact-message">
              <h2 class="contact-title">
                <div class="title-line">你需要的是一位</div>
                <div class="title-line">
                  共同守护公共健康的<span class="partner-text">合作伙伴</span>
                </div>
                <div class="title-line">而不是乙方</div>
              </h2>
            </div>

            <!-- 右下角联系信息 -->
            <div class="contact-info-section">
              <!-- 滑动白线 -->
              <div class="sliding-line"></div>

              <!-- 联系信息内容 -->
              <div class="contact-details-new">
                <div class="contact-slogan">积极沟通，不断思考</div>
                <div class="contact-person-info">周先生：13316534019</div>
                <div class="contact-qrcode-new">
                  <img
                    src="assets/first_page/contacts_qrcode.png"
                    alt="周总微信二维码"
                    class="qrcode-full"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
    <div id="footer-container"></div>
    <script src="js/common.js"></script>
    <script src="js/index.js"></script>
    <script src="js/footer.js"></script>
  </body>
</html>

/* products.html 专用样式 */

/* 产品页面主容器 */
.products-main {
  padding-top: 80px;
}

/* 产品中心头部 */
.products-hero {
  padding: 80px 0 60px;
  background: #ffffff;
  position: relative;
  overflow: hidden;
}

.products-hero .container {
  max-width: 95%;
  margin: 0 auto;
  padding: 0 max(3%, 30px);
}

.products-header {
  position: relative;
  z-index: 2;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 80px;
  min-height: 400px;
}

.header-left {
  flex: 0 0 auto;
  min-width: 350px;
}

.products-title {
  font-size: 8rem;
  font-weight: 900;
  color: #f0f0f0;
  text-transform: uppercase;
  letter-spacing: -0.02em;
  line-height: 0.9;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 50%, #a5b4fc 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.header-right {
  flex: 1;
  margin-left: 100px;
}

.solutions-info {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(226, 232, 240, 0.3);
  border-radius: 24px;
  padding: 40px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
}

.solutions-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 20px;
  line-height: 1.2;
}

.solutions-description {
  font-size: 1.2rem;
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0;
}

.highlight-text {
  color: var(--primary-blue);
  font-weight: 600;
}

/* 浮动的毛玻璃球 */
.glass-ball-2 {
  position: absolute;
  top: 15%;
  left: 8%;
  width: 250px;
  height: 250px;
  background: radial-gradient(circle, rgba(99, 102, 241, 0.12) 0%, rgba(139, 92, 246, 0.08) 50%, transparent 70%);
  backdrop-filter: blur(20px);
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.3);
  animation: floatGlassBall1 15s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
}

.glass-ball-3 {
  position: absolute;
  top: 45%;
  right: 12%;
  width: 180px;
  height: 180px;
  background: radial-gradient(circle, rgba(168, 85, 247, 0.1) 0%, rgba(139, 92, 246, 0.06) 50%, transparent 70%);
  backdrop-filter: blur(15px);
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.25);
  animation: floatGlassBall2 18s ease-in-out infinite reverse;
  pointer-events: none;
  z-index: 1;
}

.glass-ball-4 {
  position: absolute;
  bottom: 20%;
  left: 15%;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.08) 0%, rgba(99, 102, 241, 0.05) 50%, transparent 70%);
  backdrop-filter: blur(18px);
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: floatGlassBall3 20s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
}

.glass-ball-5 {
  position: absolute;
  top: 60%;
  right: 35%;
  width: 120px;
  height: 120px;
  background: radial-gradient(circle, rgba(236, 72, 153, 0.1) 0%, rgba(168, 85, 247, 0.06) 50%, transparent 70%);
  backdrop-filter: blur(12px);
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.3);
  animation: floatGlassBall4 14s ease-in-out infinite reverse;
  pointer-events: none;
  z-index: 1;
}

.glass-ball-6 {
  position: absolute;
  bottom: 35%;
  right: 8%;
  width: 160px;
  height: 160px;
  background: radial-gradient(circle, rgba(34, 197, 94, 0.08) 0%, rgba(59, 130, 246, 0.05) 50%, transparent 70%);
  backdrop-filter: blur(16px);
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.25);
  animation: floatGlassBall5 16s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
}

/* 毛玻璃球动画 */
@keyframes floatGlassBall1 {
  0%, 100% { transform: translate(0, 0) rotate(0deg); }
  25% { transform: translate(30px, -20px) rotate(90deg); }
  50% { transform: translate(-20px, 30px) rotate(180deg); }
  75% { transform: translate(40px, 10px) rotate(270deg); }
}

@keyframes floatGlassBall2 {
  0%, 100% { transform: translate(0, 0) scale(1); }
  33% { transform: translate(-25px, 20px) scale(1.1); }
  66% { transform: translate(35px, -15px) scale(0.9); }
}

@keyframes floatGlassBall3 {
  0%, 100% { transform: translate(0, 0) rotate(0deg) scale(1); }
  50% { transform: translate(-30px, -25px) rotate(180deg) scale(1.05); }
}

@keyframes floatGlassBall4 {
  0%, 100% { transform: translate(0, 0); }
  25% { transform: translate(20px, -30px); }
  50% { transform: translate(-15px, 25px); }
  75% { transform: translate(25px, 15px); }
}

@keyframes floatGlassBall5 {
  0%, 100% { transform: translate(0, 0) rotate(0deg); }
  20% { transform: translate(15px, -20px) rotate(72deg); }
  40% { transform: translate(-25px, 10px) rotate(144deg); }
  60% { transform: translate(20px, 25px) rotate(216deg); }
  80% { transform: translate(-10px, -15px) rotate(288deg); }
}

/* 产品展示区域 */
.products-showcase {
  padding: 80px 0;
  background: #f8fafb;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 40px;
  margin-top: 40px;
}

.product-card {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid rgba(226, 232, 240, 0.3);
}

.product-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.12);
}

.product-image {
  position: relative;
  height: 250px;
  overflow: hidden;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.product-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-card:hover .product-img {
  transform: scale(1.05);
}

.product-content {
  padding: 30px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.product-info {
  flex: 1;
}

.product-category {
  display: inline-block;
  background: rgba(0, 102, 255, 0.1);
  color: var(--primary-blue);
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  margin-bottom: 12px;
}

.product-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 12px;
  line-height: 1.3;
}

.product-summary {
  font-size: 1rem;
  color: var(--text-secondary);
  line-height: 1.5;
  margin: 0;
}

.product-expand {
  flex: 0 0 auto;
  margin-left: 20px;
}

.expand-btn {
  width: 40px;
  height: 40px;
  border: none;
  background: rgba(0, 102, 255, 0.1);
  color: var(--primary-blue);
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.expand-btn:hover {
  background: var(--primary-blue);
  color: white;
  transform: scale(1.1);
}

.expand-btn i {
  transition: transform 0.3s ease;
}

.product-details {
  padding: 0 30px 30px;
  display: none;
}

.product-description {
  font-size: 1rem;
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0;
}

/* 响应式设计 - 移动端适配 */
@media (max-width: 768px) {
  .products-hero {
    padding: 60px 0 40px;
  }

  .header-content {
    flex-direction: column;
    gap: 40px;
    min-height: auto;
    text-align: center;
  }

  .header-left {
    min-width: auto;
    width: 100%;
  }

  .products-title {
    font-size: 4rem;
  }

  .header-right {
    margin-left: 0;
    width: 100%;
  }

  .solutions-info {
    padding: 30px 20px;
  }

  .solutions-title {
    font-size: 2rem;
  }

  .solutions-description {
    font-size: 1rem;
  }

  .products-grid {
    grid-template-columns: 1fr;
    gap: 30px;
    margin-top: 30px;
  }

  .product-card {
    border-radius: 16px;
  }

  .product-image {
    height: 200px;
  }

  .product-content {
    padding: 20px;
    flex-direction: column;
    gap: 15px;
  }

  .product-expand {
    margin-left: 0;
    align-self: flex-end;
  }

  .product-details {
    padding: 0 20px 20px;
  }

  /* 隐藏部分毛玻璃球以减少移动端视觉干扰 */
  .glass-ball-4,
  .glass-ball-5,
  .glass-ball-6 {
    display: none;
  }

  .glass-ball-2,
  .glass-ball-3 {
    transform: scale(0.6);
  }
}
